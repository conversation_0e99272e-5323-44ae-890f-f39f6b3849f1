/*
 * @Date:  2024-05-25 14:06:59
 * @LastEditors: LDH <EMAIL>
 * @LastEditTime: 2023-03-29 15:52:57
 * @FilePath: src\api\workflow.js
 * 工作流 相关接口
 */
import http from '@/utils/axios';

// 获取模版设计列表
export function getBpmnConflistPage(pageDto, taskMgmtVO) {
  let data = {
    "pageDto": pageDto,
    "entity": taskMgmtVO
  }
  return http.post(`/bpmnConf/listPage`, data)
}

// 删除模版设计
export function deleteBpmnConf(data) {
  return http.post(`/bpmnConf/delete`, data)
}

// 获取发起人的表单权限信息
export function getPromoterFormAuth(data) {
  return http.get(`/bpmnConf/process/perm/${data.templateId}`)
}

// 获取可见范围
export function getVisibleRange(data) {
  return http.get(`/bpmnConf/visible/perm/${data.confId}`)
}

// 设置可见范围
export function setVisibleRange(data) {
  return http.post(`/bpmnConf/visible/modifyPerm`, data)
}





/**
 * 获取流程配置详情
 * @param { * } data 
 * @returns 
 */
export function getApiWorkFlowData(data) {
  return http.get(`/bpmnConf/detail/${data.id}`)
}

/**
 * 获取DIY FromCode 
 * @returns 
 */
export function getDIYFromCodeData() {
  return http.get(`/bpmnBusiness/getDIYFormCodeList`)
}

/**
 * 设置/添加审批流程配置详情
 * @param {*} data 
 * @returns 
 */
export function setApiWorkFlowData(data) {
  return http.post(`/bpmnConf/edit`, data)
}
/**
* 获取代办事项 
* @returns 
*/
export function getTodoList() {
  return http.get(`/bpmnConf/todoList`)
}

/**
 * 获取抄送给我流程列表
 * @param {*} pageDto 
 * @param {*} taskMgmtVO 
 * @returns 
 */
export function getCopyToMelistPage(pageDto, taskMgmtVO) {
  let data = {
    "pageDto": pageDto,
    "taskMgmtVO": taskMgmtVO
  }
  return http.post(`/bpmnConf/process/listPage/9`, data)
}

/**
 * 获取所有实例列表
 * @param {*} pageDto 
 * @param {*} taskMgmtVO 
 * @returns 
 */
export function getAllProcesslistPage(pageDto, taskMgmtVO) {
  let data = {
    "pageDto": pageDto,
    "taskMgmtVO": taskMgmtVO
  }
  return http.post(`/bpmnConf/process/listPage/6`, data)
}

/**
 * 获取用户代办数据列表
 * @param {*} pageDto 
 * @param {*} taskMgmtVO 
 * @returns 
 */
export function getPenddinglistPage(pageDto, taskMgmtVO) {
  let data = {
    "pageDto": pageDto,
    "taskMgmtVO": taskMgmtVO
  }
  return http.post(`/bpmnConf/process/listPage/5`, data)
}

/**
 * 获取用户已审批数据列表
 * @param {*} pageDto 
 * @param {*} taskMgmtVO 
 * @returns 
 */
export function getApprovedlistPage(pageDto, taskMgmtVO) {
  let data = {
    "pageDto": pageDto,
    "taskMgmtVO": taskMgmtVO
  }
  return http.post(`/bpmnConf/process/listPage/4`, data)
}

/**
 * 获取我发起的流程列表
 * @param {*} pageDto 
 * @param {*} taskMgmtVO 
 * @returns 
 */
export function getMyRequestlistPage(pageDto, taskMgmtVO) {
  let data = {
    "pageDto": pageDto,
    "taskMgmtVO": taskMgmtVO
  }
  return http.post(`/bpmnConf/process/listPage/3`, data)
}

// 获取申请记录列表
export function getRequestlistPage(pageDto, taskMgmtVO, status) {
  let data = {
    "pageDto": pageDto,
    "taskMgmtVO": taskMgmtVO
  }
  return http.post(`/bpmnConf/process/listPage/${status}`, data)
}

/**
 * 审批,发起审批
 * @param {*} data 
 * operationType 1 发起 2 重新提交 3 审批
 * @returns 
 */
export function processOperation(data) {
  return http.post(`/bpmnConf/process/buttonsOperation?formCode=${data.formCode}`, data)
}

/**
 * 获取审批进度数据
 * @param { Number } id 
 * @returns 
 */
export function getBpmVerifyInfoVos(param) {
  return http.get(`/bpmnConf/getBpmVerifyInfoVos?processNumber=${param.processNumber}`)
}

/**
 * 流程预览
 * @param {*} param 
 * @returns 
 */
export function getFlowPreview(data) {
  // let paramA = {
  //   "formCode": "DSFZH_WMA",
  //    "accountType":1
  // }
  return http.post(`/bpmnConf/startPagePreviewNode`, data)
}


/**
 * 流程生效
 * @param {*} data 
 * @returns 
 */
export function getEffectiveBpmn(data) {
  return http.get(`/bpmnConf/effectiveBpmn/${data.id}`)
}

/**
 * 获取审批页面按钮权限
 * @param {*} data 
 * @returns 
 */
export function getViewBusinessProcess(data) { 
  return http.post(`/bpmnConf/process/viewBusinessProcess?formCode=${data.formCode}`, data)
}

/**
* 获取审批页面 审批人配置类型 
* @returns 
*/
export function getApproveNodeProperties() {
  return http.get(`/bpmnBusiness/listNodeProperties`)
}
 
/**
 * 获取委托列表
 * @param {*} pageDto 
 * @param {*} taskMgmtVO 
 * @returns 
 */
export function getUserEntrustListPage(pageDto, taskMgmtVO) {
  let data = {
    "pageDto": pageDto,
    "taskMgmtVO": taskMgmtVO
  }
  return http.post(`/bpmnBusiness/entrustlist/1`, data)
}
/**
 * 获取委托列表
 * @param {*} pageDto 
 * @param {*} taskMgmtVO 
 * @returns 
 */
export function getEntrustListPage(pageDto, taskMgmtVO) {
  let data = {
    "pageDto": pageDto,
    "taskMgmtVO": taskMgmtVO
  }
  return http.post(`/bpmnBusiness/entrustlist/2`, data)
}
/**
*  委托详情
* @returns 
*/
export function entrustDetail(id) {
  return http.get(`/bpmnBusiness/entrustDetail/${id}`)
}
/**
* 设置委托
* @returns 
*/
export function setEntrust(data) {
  return http.post(`/bpmnBusiness/editEntrust`, data)
}
/**
 * 获取流程自选审批人节点
 * @param {*} formCode 
 * @returns 
 */
export function getStartUserChooseModules(formCode) {
  return http.get(`/bpmnBusiness/getStartUserChooseModules?formCode=${formCode}`)
}

// 发起审批时 根据表单信息实时获取自选审批人
export function getFollowNodes(data) {
  return http.post(`/bpmnConf/getFollowNodes`, data) 
}

// 审批详情页-下载pdf
export function downloadPdf(data) {
  return http.post(`/template/pdf/export?formCode=${data.formCode}`, data, {responseType: 'blob'})
}

// 模版设计-导出excel
export function exportExcel(data) {
  return http.post(`bpmnConf/excel`, data, {responseType: 'blob'})
}