import http from '@/utils/axios'

// 获取所有标签列表
export function getAllRoleList() {
  return http.get(`/tag/getAllRole`)
}


// 获取分页标签列表
export function getTagPageList(data) {
  return http.post(`/tag/getTagPageList`, data)
}

// 新增标签
export function addTag(data) {
  return http.post(`/tag/addTag`, data)
}

// 编辑标签
export function updateTag(data) {
  return http.post(`/tag/updateTag`, data)
}
// 删除标签
export function deleteTag(roleId) {
  return http.get(`/tag/deleteTag?roleId=${roleId}`)
}

// 获取成员列表
export function getOrgTree(data) {
  return http.post(`/user/getOrgTree`, data)
}

// 模糊查询成员列表 查询类型1-根据用户名模糊搜索 2-根据组织名称模糊搜索 3-根据用户组织模糊搜索 4-根据用户标签模糊搜索 5-根据用户组织标签模糊搜索
export function queryByNameFuzzy(data) {
  return http.get(`/user/queryByNameFuzzy?name=${data.name}&queryType=${data.queryType}`)
}
