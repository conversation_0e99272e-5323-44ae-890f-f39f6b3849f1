<template>
  <div class="my-nav-content">
    <div id="designer-id" class="form-container">
      <v-form-designer ref="formDesign"></v-form-designer> 
    </div>
    <!-- <button @click="submitForm">ok</button> -->
  </div>
</template>

<script setup>
import { ref,onUnmounted, onMounted,computed, watch } from 'vue'
import { useRoute } from 'vue-router';
import { ObjectUtils } from '@/utils/ObjectUtils'
import { useStore } from '@/store/modules/workflow'
let store = useStore()
const route = useRoute();
let props = defineProps({
  lfFormData: {
    type: String,
    default: null,
  }
}); 
let lfFormDataConf = computed(()=> props.lfFormData) 
const formDesign = ref(null)
let formField = {};
const observer = new MutationObserver(() => {
  let widgetList = formDesign.value.getFormJson()?.widgetList || [];
  widgetList = widgetList.map(v => v.options);
  let returnFiled = { formFields: widgetList }
  if (ObjectUtils.isObjectChanged(formField, returnFiled)) {
    formField = returnFiled; 
    store.setLowCodeFormField(formField);
  }
}); 

onMounted(() => {
  const targetNode = document.querySelector('#designer-id');
  const config = { childList: true, subtree: true };
  observer.observe(targetNode, config); 
  if (!route.query.id && formDesign.value) {
    formDesign.value.clearDesigner();
  }
}); 

watch(lfFormDataConf, (val) => {  
  if(val){ 
      formDesign.value.clearDesigner();
      formDesign.value.designer.loadFormJson(JSON.parse(val));
   } 
},{deep:true,immediate:true})
 
onUnmounted(() => {
  observer.disconnect();
});
const getData = () => {
  let exportData = formDesign.value.getFormJson();
  // console.log('exportData=========', exportData)
  return new Promise((resolve, reject) => {
    resolve({ formData: exportData })
  })
}

defineExpose({
  getData
})
</script>

<style lang="scss" scoped>
body {
  margin: 0;
  /* 如果页面出现垂直滚动条，则加入此行CSS以消除之 */
} 
.el-dialog {
  width: 700px !important;
  border: 1px solid #DDE1E5 !important;
  border-radius: 3px !important;
} 
.form-container {
  background: white !important;
  padding: 0px;
  width: 100%;
  left: 0;
  bottom: 0;
  right: 0;
  margin: auto;
} 
.main-container {
  margin-left: 0px !important;
}
</style>