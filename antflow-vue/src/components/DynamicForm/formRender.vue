<template>
  <div class="form-container">
    <v-form-render ref="vFormRef" :form-json="formJson" :form-data="formData" :option-data="optionData" @formChange="handleFormChange">
    </v-form-render>
    <el-button v-if="!isPreview && props.showSubmit" type="primary" @click="submitForm">提交</el-button>
    <div style="margin-top: 20px;">
      <TagApproveSelect
        ref="tagApproveSelectRef"
        v-if="hasChooseApprove == 'true' || hasChooseCC == 'true'"
        :formCode="formCode"
        :formInfo="formInfo"
        @chooseApprove="chooseApprovers" />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, getCurrentInstance, onBeforeMount, onMounted } from 'vue';
import TagApproveSelect from "@/components/BizSelects/TagApproveSelect/index.vue";
const isEmpty = data => data === null || data === undefined || data == '' || data == '{}' || data == '[]' || data == 'null';
const { proxy } = getCurrentInstance();
const route = useRoute();
const formCode = route.query?.formCode ?? '';
const hasChooseApprove = route.query?.hasChooseApprove ?? 'false';
const hasChooseCC = route.query?.hasChooseCC ?? 'false';
let props = defineProps({
  lfFormData: {//业务表单字段
    type: String,
    default: "{}",
  },
  lfFieldsData: {//表单字段输入值
    type: String,
    default: "{}",
  },
  lfFieldPerm: {//表单字段控制权限
    type: String,
    default: "[]",
  },
  showSubmit: {//是否显示提交按钮
    type: Boolean,
    default: false,
  },
  isPreview: {//是否预览
    type: Boolean,
    default: true,
  },
  isDisabled: { //是否禁用所有表单项
    type: Boolean,
    default: false,
  },
  isResubmit: {//是否重新提交
    type: Boolean,
    default: false,
  },
  lfFlowNodes: {
    type: Array,
    default: () => {
      return []
    }
  }
});
/* 注意：formJson是指表单设计器导出的json，此处演示的formJson只是一个空白表单json！！ */
const formJson = reactive(JSON.parse(props.lfFormData));//表单控件渲染
const formData = reactive(JSON.parse(props.lfFieldsData));//表单字段输入值渲染 
const lfFieldPermData = reactive(JSON.parse(props.lfFieldPerm));//表单字段权限控制 
const optionData = reactive({});
const vFormRef = ref(null);
const tagApproveSelectRef = ref(null);
const formInfo = ref({});

/**表单渲染预处理 */
const advanceHandleFormData = () => {
  if (!props.isPreview) { // 预览时，不进行表单字段权限控制
    traverseFieldWidgetsList(formJson.widgetList, handlerFn);
  }
}
/**表单字段权限控制 */
const handlerFn = (w) => {
  if (!isEmpty(props.lfFieldPerm)) { 
    let info = lfFieldPermData.find(function (ele) { return ele.fieldId == w.options.name; }); 
    if (info) {
      w.options.perm = info.perm;
      if (info.perm == 'R') {
        w.options.disabled = true;
      } else if (info.perm == 'E') {
        w.options.readonly = false;
        w.options.disabled = props.isDisabled || w.options.disabled;
      } else if (info.perm == 'H') {//隐藏字段处理：将所以字段类型转化为input格式，value 赋值为 ******
        w.options.hidden = true
        // w.type = 'input';
        // w.options.type = 'text';
        // formData[w.options.name] = '******';
        // delete w.options.format;
        // delete w.options.valueFormat;
        // w.options.disabled = true;
      } else { 
        w.options.disabled = props.isDisabled || false;
        w.options.readonly = true;
      }
    }
  } else { 
    // 可编辑：重新提交isResubmit为true  提交表单showSubmit为true
    if(props.isResubmit || props.showSubmit) {
      w.options.disabled = props.isDisabled || false;
      w.options.readonly = false;
    } else {
      w.options.disabled = true;
      w.options.readonly = true;
    }
  }
}
/**递归处理表单中所有字段 */
const traverseFieldWidgetsList = function (widgetList, handler) {
  if (!widgetList) {
    return
  }
  widgetList.map(w => {
    if (w.formItemFlag || w.type === 'alert') {
      handler(w)
    } else if (w.type === 'grid') {
      w.cols.map(col => {
        traverseFieldWidgetsList(col.widgetList, handler, w)
      })
    } else if (w.type === 'table') {
      w.rows.map(row => {
        row.cols.map(cell => {
          traverseFieldWidgetsList(cell.widgetList, handler, w)
        })
      })
    } else if (w.type === 'tab') {
      w.tabs.map(tab => {
        traverseFieldWidgetsList(tab.widgetList, handler, w)
      })
    } else if (w.type === 'sub-form') {
      traverseFieldWidgetsList(w.widgetList, handler, w)
    } else if (w.category === 'container') {  //自定义容器
      traverseFieldWidgetsList(w.widgetList, handler, w)
    }
  })
}
onBeforeMount(() => {
  console.log("isPreview======", JSON.stringify(props.isPreview));
  console.log("showSubmit======", JSON.stringify(props.showSubmit));
  advanceHandleFormData();
})

onMounted(() => {
  if(hasChooseApprove == 'true' || hasChooseCC == 'true') {
    let conditionList = []
    props.lfFlowNodes?.filter(item => item.nodeType == 3 && item.conditionList && item.conditionList.length).forEach(item => {
      conditionList.push(...item.conditionList)
    })
    let widgetIds = conditionList.map(item => item.columnDbname)
    
    formInfo.value = {
      formDataModel: vFormRef.value?.getFormData(false) || {},
      widgetIds: [...new Set(widgetIds)]
    }
  }
})

const submitForm = () => {
  getFromData().then(res => {
    proxy.$emit("handleBizBtn", res)
  })
}

// 表单值变化 fieldName数组[控件唯一标识，控件新值，控件旧值，子表单名，子表单行索引], newValue新值(还没值，用来占位的), oldValue旧值(还没值，用来占位的), formDataModel表单数据, subFormName子表单名, subFormRowIndex子表单行索引
const handleFormChange = (fieldName, newValue, oldValue, formDataModel, subFormName, subFormRowIndex) => {
  if(tagApproveSelectRef.value) {
    tagApproveSelectRef.value.handleFormChange(fieldName, formDataModel)
  }
}

// 表单验证
const handleValidate = () => {
  return new Promise((resolve, reject) => {
    try {
      vFormRef.value.validateForm((isValid) => {
        if (!isValid) {
          reject(false);
        } else {
          if ((hasChooseApprove == 'true' || hasChooseCC == 'true') && (!formData.approversValid || formData.approversValid == false)) {
            proxy.$modal.msgError('请选择自选审批人');
            reject(false);
          }
          else {
            resolve(isValid);
          }
        }
      });
    } catch (error) {
      reject(false);
    }
  });
}

// 获取表单值
const getFromData = () => {
  return new Promise((resolve, reject) => {
    try {
      vFormRef.value.getFormData().then(res => {
        if (hasChooseApprove == 'true' || hasChooseCC == 'true') {
          Object.assign(res, {
            approversList: formData.approversList,
            approversValid: formData.approversValid
          });
        }
        resolve(JSON.stringify(res));
      }).catch(err => {
        reject("");
      })
    } catch (error) {
      reject("");
    }
  });
}

/**自选审批人 */
const chooseApprovers = (data) => {
  formData.approversList = data.approvers;
  formData.approversValid = data.nodeVaild;
}

// 获取表单信息
const getFromInfo = () => {
  return new Promise((resolve) => {
    let formData = vFormRef.value?.formJson || {}
    resolve({ formData })
  })
}

defineExpose({
  handleValidate,
  getFromData,
  getFromInfo
})
</script>
<style scoped lang="scss">
.form-container {
  background: white !important;
  padding: 10px;
  // max-width: 750px;
  min-height: 100%;
  left: 0;
  bottom: 0;
  right: 0;
  margin: auto;
}
</style>