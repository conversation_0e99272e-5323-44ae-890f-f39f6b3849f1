<template>
  <div class="org-member-wrap">
    <el-tree-select
      ref="treeRef"
      v-model="pageData.memberOrgIds"
      :data="pageData.treeList"
      node-key="value"
      :multiple="pageData.selectProps.multiple"
      clearable
      filterable
      remote
      :remote-method="remoteMethod"
      :loading="pageData.optionLoading"
      :load="loadNode"
      :props="{ isLeaf: 'isLeaf' }"
      lazy
      @node-click="handleNodeClick"
      @clear="handleClear"
    >
      <template #default="{ node, data }">
        <div class="custom-tree-node">
          <span v-if="pageData.showQueryList && data.namePath" class="tree-node-label"
            >{{ `${formattedNamePath(data.namePath)}` }}：</span
          >
          <span class="tree-node-label">{{ node.label }}</span>
        </div>
      </template>
      <template v-if="pageData.selectProps.multiple" #tag>
        <div class="select-tag">
          <el-tag
            v-for="item in pageData.memberOrgList"
            :key="`${item.ehrSource}_${item.id}`"
            closable
            type="info"
            @close="handleRemove(item)"
            class="ellipsis-tag"
          >
            <span class="tag-text">{{
              item.namePath ? `${item.namePath}/${item.name}` : item.name
            }}</span>
          </el-tag>
        </div>
      </template>
      <template v-else #label>
        <div class="select-tag">
          <el-tag
            v-for="item in pageData.memberOrgList"
            :key="`${item.ehrSource}_${item.id}`"
            closable
            type="info"
            @close="handleRemove(item)"
            class="ellipsis-tag"
          >
            <span class="tag-text">{{
              item.namePath ? `${item.namePath}/${item.name}` : item.name
            }}</span>
          </el-tag>
        </div>
      </template>
    </el-tree-select>
  </div>
</template>

<script setup>
import { reactive, ref } from "vue";
import { getOrgTree, queryByNameFuzzy } from "@/api/label";
import { EHR_SOURCE } from "@/utils/constants";

const emits = defineEmits(["change", "update:dataList"]);
const props = defineProps({
  type: {
    // 1成员 2部门
    type: [String, Number],
    default: "1",
  },
  dataList: {
    type: [String, Array],
    default: () => [],
  },
  selectProps: {
    type: Object,
    default: () => {
      multiple: true;
    },
  },
});
const treeRef = ref();
const pageData = reactive({
  treeList: EHR_SOURCE, // 树数据列表
  memberOrgList: [], // 选择的人/组织
  memberOrgIds: "", // 选择的人/组织 id
  optionLoading: false,
  showQueryList: false, // 是否显示模糊查询列表
  selectProps: {
    multiple: true,
    ...(props.selectProps || {}),
  },
});

// 获取ehrSource的label
const getEhrSourceLabel = (ehrSource) => {
  return EHR_SOURCE.find((item) => item.ehrSource == ehrSource)?.label;
};

onMounted(() => {
  pageData.memberOrgList = (props.dataList || []).map((item) => {
    let id = item.id;
    if (item.controlDataType == 2) {
      id = `${item.ehrSource}_${id}`;
    }
    return { ...item, id };
  });
  setTreeChecked();
});

// 获取树列表
const getTreeList = async (ehrSource) => {
  let type = props.type || 1;
  let allTreeList = sessionStorage.getItem(`allTreeList_${type}`);
  allTreeList = allTreeList ? JSON.parse(allTreeList) : {};
  if (allTreeList[ehrSource]?.length) {
    return allTreeList[ehrSource];
  }

  let params = {
    ehrSource,
    type,
  };
  let res = await getOrgTree(params);
  allTreeList[ehrSource] = res?.data?.children || [];
  sessionStorage.setItem(`allTreeList_${type}`, JSON.stringify(allTreeList));
  return allTreeList[ehrSource];
};

// 格式化namePath
const formattedNamePath = (namePath) => {
  const label = namePath || "";
  if (label.length <= 15) return label;

  return label.substring(0, 7) + "..." + label.substring(label.length - 8);
};

// 懒加载
const loadNode = async (node, resolve, reject) => {
  console.log("loadNode: ", node);
  if (!node || node.level === 0) {
    return resolve(EHR_SOURCE);
  }
  if (node.level === 1) {
    try {
      let res = await getTreeList(node.data.ehrSource);
      // 如果某个节点没有 children，手动标记为 leaf: true
      const processedData = res.map((item) => ({
        ...item,
        isLeaf: !item.children || item.children.length === 0,
      }));
      setTreeChecked();
      return resolve(processedData);
    } catch (error) {
      return reject(error);
    }
  }
  // 如果已经是最后一级，返回空数组并标记 leaf: true
  if (!node.data.children || node.data.children.length === 0) {
    return resolve([]);
  }
  // 否则返回子节点，并检查是否需要标记 leaf
  const children = node.data.children.map((child) => ({
    ...child,
    isLeaf: !child.children || child.children.length === 0,
  }));
  return resolve(children);
};
// 点击树节点
const handleNodeClick = (node, nodeProp) => {
  if (node.type != props.type) {
    nodeProp.checked = false;
    return;
  }

  let empId = node.value;
  let empIds = pageData.memberOrgList.map((item) => item.id);
  const isChecked = (nodeProp.checked = !nodeProp.checked);
  const isExisting = empIds.includes(empId);
  let ehrSource = "";
  let namePath = nodeProp.data.namePath || "";
  if (nodeProp.level === 1) {
    ehrSource = nodeProp.data.ehrSource;
    namePath = nodeProp.data.namePath || nodeProp.data.label;
  } else {
    let parent = nodeProp.parent;
    while (parent && parent.level !== 1) {
      if (!nodeProp.data.namePath) {
        namePath = namePath ? parent?.data?.label + "/" + namePath : parent?.data?.label;
      }
      parent = parent.parent;
    }
    ehrSource = parent?.data?.ehrSource || "";
    if (!nodeProp.data.namePath) {
      namePath = parent?.data?.label + "/" + namePath;
    }
  }
  if (isChecked && !isExisting) {
    // 选中 但不存在
    let currentItem = {
      ehrSource,
      namePath,
      controlDataType: node.type,
      id: empId,
      name: node.label,
    };
    if (pageData.selectProps.multiple) {
      pageData.memberOrgList.push(currentItem);
    } else {
      pageData.memberOrgList = [currentItem];
    }
  } else if (!isChecked && isExisting) {
    // 取消选中 但存在
    pageData.memberOrgList = pageData.memberOrgList.filter(
      (item) =>
        item.ehrSource != ehrSource || (item.ehrSource == ehrSource && item.id != empId)
    );
  }
  setTreeChecked();
  emitOuter();
};
// 模糊查询
const fetchQueryData = (query) => {
  let params = {
    name: query.trim(),
    queryType: props.type
  }
  pageData.optionLoading = true
  queryByNameFuzzy(params).then(response => {
    let list = props.type == 1 ? response?.data?.users : response?.data?.orgs
    pageData.treeList = list.map(item => ({
      ...item,
      label: item.name,
      ehrSource: item.ehrSource,
      value: item.id,
      isLeaf: true
    }))
    // treeList更换时重新设置选中
    setTreeChecked()
  }).finally(() => {
    pageData.showQueryList = true
    pageData.optionLoading = false
  })
}
// 远程获取搜索节点数据
const remoteMethod = (query) => {
  if (query) {
    fetchQueryData(query);
  } else {
    pageData.showQueryList = false;
    pageData.treeList = EHR_SOURCE;
    // treeList更换时重新设置选中
    setTreeChecked();
  }
};

// 删除标签
const handleRemove = (item) => {
  // 移除选中 返回ehrSource不同 或者 ehrSource相同但id不同
  pageData.memberOrgList = pageData.memberOrgList.filter(
    (el) =>
      item.ehrSource != el.ehrSource ||
      (item.ehrSource == el.ehrSource && item.id != el.id)
  );
  setTreeChecked();
  emitOuter();
};
// 清空
const handleClear = () => {
  pageData.memberOrgList = [];
  setTreeChecked();
  emitOuter();
};

const setTreeChecked = () => {
  if (treeRef.value) {
    if (pageData.selectProps.multiple) {
      pageData.memberOrgIds = pageData.memberOrgList.map((item) => item.id);
      treeRef.value.setCheckedKeys(pageData.memberOrgIds);
    } else {
      pageData.memberOrgIds = pageData.memberOrgList[0]?.id || "";
      treeRef.value.setCheckedKeys(pageData.memberOrgIds ? [pageData.memberOrgIds] : []);
    }
  }
};

// 向外发送数据
const emitOuter = () => {
  let outerList = pageData.memberOrgList.map((item) => {
    let id = item.id;
    if (item.controlDataType == 2) {
      id = id.split("_")[1];
    }
    return {
      ...item,
      id,
    };
  });
  console.log("emitOuter: ", pageData.memberOrgList, outerList);
  emits("change", outerList);
  emits("update:dataList", outerList);
};
</script>

<style lang="scss" scoped>
.org-member-wrap {
  width: 100% !important;
  .select-tag {
    max-width: 100%;
    box-sizing: border-box;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    .ellipsis-tag {
      max-width: 100%;
      box-sizing: border-box;
      :deep(.el-tag__content) {
        max-width: 100%;
        box-sizing: border-box;
        .tag-text {
          max-width: 100%;
          box-sizing: border-box;
          display: inline-block;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          vertical-align: middle;
        }
      }
    }
  }
}
</style>
