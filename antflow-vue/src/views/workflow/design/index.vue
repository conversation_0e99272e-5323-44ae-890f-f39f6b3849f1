<template>
  <div class="app-container">
     <div class="query-box">
        <el-form :model="pageData.filterForm" :inline="true">
           <el-form-item label="模版编号" prop="id">
              <el-input v-model="pageData.filterForm.id" placeholder="请输入模版编号" clearable style="width: 200px"
                 @keyup.enter="handleQuery" />
           </el-form-item>
           <el-form-item label="模版名称" prop="bpmnName">
              <el-input v-model="pageData.filterForm.bpmnName" placeholder="请输入模版名称" clearable style="width: 200px"
                 @keyup.enter="handleQuery" />
           </el-form-item>
           <el-form-item label="模版分组" prop="templateGroupIds">
              <el-select v-model="pageData.filterForm.templateGroupIds" placeholder="请选择模版分组" clearable multiple collapse-tags	collapse-tags-tooltip style="width: 200px">
                 <el-option
                    v-for="item in pageData.templateGroupOptions"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                 />
              </el-select>
           </el-form-item>
           <el-form-item label="模版状态" prop="status">
              <el-select v-model="pageData.filterForm.status" placeholder="请选择模版状态" clearable style="width: 200px">
                 <el-option
                    v-for="item in pageData.templateStatusOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                 />
              </el-select>
           </el-form-item>
           <el-form-item>
              <el-button type="primary" @click="handleQuery">搜索</el-button>
              <el-button @click="resetQuery">重置</el-button>
           </el-form-item>
        </el-form>
        <div>
          <el-button type="primary" plain @click="handleDesign">模版设计</el-button>
        </div>
     </div>
     <div class="table-box">
        <el-table v-loading="pageData.loading" :data="pageData.dataList">
           <el-table-column label="模版编号" width="76" align="center" prop="id" show-overflow-tooltip />
           <el-table-column label="模版名称" align="center" prop="bpmnName" show-overflow-tooltip />
           <el-table-column label="模版分组" align="center" prop="templateGroupName" show-overflow-tooltip />
           <el-table-column label="模版说明" align="center" prop="remark" show-overflow-tooltip />
           <el-table-column label="创建人" width="68" align="center" prop="createUser" show-overflow-tooltip />
           <el-table-column label="模版状态" width="80" align="center" prop="status">
            <template #default="scope">
              <el-tag v-if="scope.row.status === 1" type="danger">已禁用</el-tag>
              <el-tag v-else-if="scope.row.status === 0" type="success">启用中</el-tag>
            </template>
           </el-table-column>
           <el-table-column label="创建时间" align="center" prop="createTime" />
           <el-table-column label="修改时间" align="center" prop="updateTime" />
           <el-table-column label="操作" width="150" align="center" class-name="small-padding">
              <template #default="scope">
                 <el-button link type="primary" @click="handlePreview(scope.row)">预览</el-button>
                 <el-button v-if="scope.row.status === 0" class="ml3" link type="primary" @click="handleVisible(scope.row)">可见范围</el-button>
                 <el-button v-if="scope.row.status === 0" class="ml3" link type="primary" @click="handleDesign(scope.row)">复制</el-button>
                 <el-button v-if="scope.row.status === 0" class="ml3" link type="danger" @click="handleDelete(scope.row)">禁用</el-button>
                 <el-button class="ml3" link type="danger" @click="handleExport(scope.row)">导出</el-button>
              </template>
           </el-table-column>
        </el-table>
        <pagination v-show="pageData.total > 0" :total="pageData.total" v-model:page="pageData.pageDto.page" v-model:limit="pageData.pageDto.pageSize"
           @pagination="getList" />
        <VisibleDrawer v-if="pageData.showVisibleDrawer" v-model:visible="pageData.showVisibleDrawer" :row="pageData.currentRow" />
        <ExportDrawer v-if="pageData.showExportDrawer" v-model:visible="pageData.showExportDrawer" :row="pageData.currentRow" />
     </div>
  </div>
</template>

<script setup>
import { reactive } from 'vue'
import { TEMPLATE_STATUS } from '@/utils/constants';
import { getBpmnConflistPage, deleteBpmnConf } from "@/api/workflow"
import { getTemplateGroupList } from "@/api/template"
import VisibleDrawer from "@/views/workflow/components/visibleDrawer.vue";
import ExportDrawer from "@/views/workflow/components/exportDrawer.vue";
const { proxy } = getCurrentInstance();

const pageData = reactive({
  pageDto: {
    page: 1,
    pageSize: 10
  },
  total: 0,
  filterForm: {
    id: '',
    bpmnName: '',
    templateGroupIds: [],
    status: 0
  },
  templateGroupOptions: [],
  templateStatusOptions: TEMPLATE_STATUS,
  loading: true,
  dataList: [],
  showVisibleDrawer: false, // 可见范围抽屉
  showExportDrawer: false, // 导出抽屉
  currentRow: null
});

/** 查询模版列表 */
async function getList() {
  let params = {
    isOutSideProcess: 0,
    ...pageData.filterForm
  }
  pageData.loading = true;
  await getBpmnConflistPage(pageData.pageDto, params).then(response => {
    pageData.dataList = response?.data?.data || [];
    pageData.total= response?.data?.pagination?.totalCount || 0
  }).finally(() => {
    pageData.loading = false;
  })
}

/** 搜索按钮操作 */
function handleQuery() {
  pageData.pageDto.page = 1;
  getList();
}
function resetQuery() {
  pageData.filterForm = {
    id: '',
    bpmnName: '',
    templateGroupIds: [],
    status: 0
  };
  handleQuery();
}

const handleDelete = (row) => {
  proxy.$modal.confirm('确定禁用该模版吗？').then(() => {
    row.id && deleteBpmnConf({ id: row.id }).then((res) => {
      proxy.$modal.msgSuccess("禁用成功");
      handleQuery();
    })
  }).catch(() => {
    console.log('取消');
  });
}

// 模版设计 / 编辑
const handleDesign = (row) => {
  proxy.$modal.loading(); 
  const query = row.id ? { id: row.id } : {}
  proxy.$modal.closeLoading();
  const obj = { path: '/workflow/lf-design', query };
  proxy.$tab.openPage(obj);
}

// 预览
const handlePreview = (row) => {
  const query = { id: row.id }
  const obj = { path: '/workflow/preview', query }
  proxy.$tab.openPage(obj)
}

// 可见范围
const handleVisible = (row) => {
  pageData.showVisibleDrawer = true
  pageData.currentRow = row
}

// 导出
const handleExport = (row) => {
  pageData.showExportDrawer = true
  pageData.currentRow = row
}

// 获取模版分组列表
async function getTemplateGroupData() {
  let params = {
    pageDto: {
      page: 1,
      pageSize: 100,
    },
    name: ''
  }
  const res = await getTemplateGroupList(params)
  pageData.templateGroupOptions = res?.data || []
}

getTemplateGroupData();
getList();
</script>

<style lang="scss" scoped>
.ml3 {
  margin-left: 3px!important;
}
</style>