<template>
  <div>
    <el-drawer v-model="showDrawer" :before-close="closeDrawer" size="50%" :with-header="false" destroy-on-close>
      <span style="font-weight: bold;">导出</span>
      <el-divider />
      <el-form :model="pageData.filterForm" label-width="120">
        <el-form-item label="提交时间">
          <el-date-picker
            v-model="pageData.filterForm.createTime"
            type="datetimerange"
            :shortcuts="shortcuts"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :defaultTime="[
              new Date(2000, 1, 1, 0, 0, 0),
              new Date(2000, 2, 1, 23, 59, 59),
            ]"
            align="right"
            :disabled-date="disabledDate"
            value-format="YYYY-MM-DD HH:mm:ss">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="提交人">
          <MemberOrg type="1" v-model:dataList="pageData.filterForm.empInfo" :selectProps="{ multiple: false }" />
        </el-form-item>
        <el-form-item label="提交人所属部门">
          <MemberOrg type="2" v-model:dataList="pageData.filterForm.orgInfo" :selectProps="{ multiple: false }" />
        </el-form-item>
      </el-form>
      <div class="mt20">
          <el-button type="primary" @click="handleExport" :loading="pageData.saveLoading">导出</el-button>
          <el-button @click="closeDrawer">取消</el-button>
      </div>
    </el-drawer>
  </div>

</template>

<script setup>
import { reactive } from 'vue'
import MemberOrg from '@/components/MemberOrg/index.vue'
import { exportExcel } from '@/api/workflow'

const shortcuts = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setDate(start.getDate() - 7)
      return [start, end]
    },
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setMonth(start.getMonth() - 1)
      return [start, end]
    },
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setMonth(start.getMonth() - 3)
      return [start, end]
    },
  },
]

const { proxy } = getCurrentInstance()
const emit = defineEmits(['update:visible'])
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  row: {
    type: Object,
    default: null
  }
})
let showDrawer = computed({
  get() {
    return props.visible
  },
  set() {
    closeDrawer()
  }
})
let pageData = reactive({
  showOrgMember: false,
  visibleRange: [],
  saveLoading: false,
  filterForm: {
    createTime: '',
    empInfo: [],
    orgInfo: []
  }
})
// 导出
const handleExport = () => {
  let formCode = props.row?.formCode
  let params = {
    formCode,
    empInfo: pageData.filterForm.empInfo[0] || null,
    orgInfo: pageData.filterForm.orgInfo[0] || null
  }
  
  // 处理时间范围
  if (pageData.filterForm.createTime && pageData.filterForm.createTime.length === 2) {
    params.startDate = pageData.filterForm.createTime[0]
    params.endDate = pageData.filterForm.createTime[1]
  }
  delete params.createTime
  
  pageData.saveLoading = true
  exportExcel(params).then((res) => {
    // 获取二进制数据
    const blob = new Blob([res], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    
    // 创建下载链接
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    let downloadName = params.startDate && params.endDate ? `（${params.startDate}至${params.endDate}）` : '';
    downloadName = (props.row.bpmnName || '模版设计') + downloadName;
    a.download = `${downloadName}.xlsx`; // 设置下载文件名
    document.body.appendChild(a);
    a.click();
    
    // 清理
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
    
    proxy.$modal.msgSuccess('导出成功')
    closeDrawer()
  }).finally(() => {
    pageData.saveLoading = false
  })
}

// 选择创建人
const onChangeMemberList = (dataList) => {
  pageData.filterForm.empInfo = dataList || []
}

// 选择创建人所属部门
const onChangeOrgList = (dataList) => {
  pageData.filterForm.orgInfo = dataList || []
}

// 关闭抽屉
const closeDrawer = () => {
  emit('update:visible', false)
}

// 限制日期选择范围，只能选择近2年内的时间（今天及之前2年）
const disabledDate = (time) => {
  const today = new Date()
  // 计算2年前的日期
  const twoYearsAgo = new Date(today)
  twoYearsAgo.setFullYear(today.getFullYear() - 2)
  
  // 限制只能选择今天及之前2年的日期
  return time.getTime() > today.getTime() || time.getTime() < twoYearsAgo.getTime()
}
</script>