<template>
    <div class="app-container">
        <div class="card-box" style="padding-top: 10px;background-color: #fff;">
            <div class="approve-title">
                <el-tag v-if="processTitle" type="primary">{{ processTitle }}</el-tag>
                <el-tag v-if="businessId" type="success">{{ businessId }}</el-tag>
            </div>
            <el-tabs v-model="activeName" class="set-tabs">
                <el-tab-pane label="表单信息" name="baseTab">
                    <div class="approve" v-if="activeName === 'baseTab'">
                        <div v-if="approvalButtons && approvalButtons.length > 0" class="approve-buttons">
                            <el-button
                                v-for="(btn, index) in approvalButtons"
                                :key="index"
                                @click="clickApproveSubmit(btn.value)"
                                :type="approveButtonColor[btn.value]">
                                {{ btn.label }}
                            </el-button>
                        </div>
                        <div v-if="componentLoaded" class="component">
                            <component ref="componentFormRef" :is="loadedComponent" :previewData="componentData"
                                :isPreview="false" :isDisabled="!approvalButtons || !approvalButtons.length" :lfFormData="lfFormDataConfig" :isResubmit="isResubmit"
                                :lfFieldsData="lfFieldsConfig" :lfFieldPerm="lfFieldControlVOs">
                            </component>
                        </div>
                    </div>
                </el-tab-pane>
                <el-tab-pane label="审批记录" name="flowStep">
                    <div v-if="activeName === 'flowStep'">
                        <FlowStepTable />
                    </div>
                </el-tab-pane>
                <el-tab-pane label="流程预览" name="flowReview">
                    <div v-if="activeName === 'flowReview'">
                        <ReviewWarp />
                    </div>
                </el-tab-pane>
            </el-tabs>
        </div>
        <transfer-dialog v-model:visible="dialogVisible" :isMultiple="isMultiple" :title="dialogTitle" @change="sureDialogBtn" />
        <repulse-dialog v-model:visible="repulseDialogVisible" @clickConfirm="approveSubmit" />
        <approve-dialog v-model:visible="openApproveDialog" :title="approveDialogTitle" @clickConfirm="approveSubmit" />
        <label class="page-close-box" @click="close()"><img src="@/assets/images/back-close.png"></label>
    </div>
</template>

<script setup>
import { ref, watch } from 'vue';
import { getUserInfo } from "@/utils/auth";
import FlowStepTable from '@/components/Workflow/Preview/flowStepTable.vue';
import ReviewWarp from '@/components/Workflow/Preview/reviewWarp.vue'; 
import transferDialog from './components/transferDialog.vue';
import approveDialog from './components/approveDialog.vue';
import repulseDialog from './components/repulseDialog.vue';
import { approveButtonColor, approvalButtonConf } from '@/utils/flow/const';
import { getViewBusinessProcess, processOperation } from '@/api/workflow';
import { loadDIYComponent, loadLFComponent } from '@/views/workflow/components/componentload.js';
const { query } = useRoute();
const { proxy } = getCurrentInstance();
import { useStore } from '@/store/modules/workflow';
let store = useStore();
let { setPreviewDrawerConfig } = store;
const formCode = query?.formCode;
const processNumber = query?.processNumber;
const isOutSideAccess = query?.isOutSideAccess;
const isLowCodeFlow = query?.isLowCodeFlow
const taskId = query?.taskId;
const businessId = ref('');
const activeName = ref('baseTab'); 
let openApproveDialog = ref(false);
let repulseDialogVisible = ref(false);
let approveDialogTitle = ref("审批");
let dialogVisible = ref(false);
let dialogTitle = ref('');
let isMultiple = ref(false);//false 转办，true 加批
let approvalButtons = ref([]);  
let loadedComponent = ref(null);
let componentData = ref(null);
let componentLoaded = ref(false);
let lfFormDataConfig = ref(null);
let lfFieldsConfig = ref(null);
let lfFieldControlVOs = ref(null);
const componentFormRef = ref(null);
const handleClickType = ref(null);
const processTitle = ref('');
const isResubmit = ref(false);
let approveSubData = reactive({
    taskId: taskId,
    processNumber: processNumber,
    formCode: formCode,
    isOutSideAccessProc: isOutSideAccess,
    outSideType: 2,
    isLowCodeFlow: isLowCodeFlow,
    lfFields: null, //低代码表单字段
});

watch(handleClickType, (val) => {
    isMultiple.value = val == approvalButtonConf.addApproval ? true : false;
});
onMounted(async () => {
    setPreviewDrawerConfig({
        formCode: formCode,
        processNumber: processNumber,
        taskId: taskId,
        isOutSideAccess: isOutSideAccess,
        isLowCodeFlow: isLowCodeFlow,
    });
    await preview();
});
/**
 * 点击页面审批操作按钮
 */
const clickApproveSubmit = async (btnType) => {
    //console.log('btnType========',JSON.stringify(btnType))    
    handleClickType.value = btnType;
    // approvalButtonConf.addApproval, 先隐藏 之后放开
    let needValidate = [approvalButtonConf.agree, approvalButtonConf.resubmit, approvalButtonConf.repulse]
    if (needValidate.includes(btnType)) {
        componentFormRef.value?.handleValidate().then(async (isValid) => {
            if (isValid) { 
                switch (btnType) {
                    case approvalButtonConf.agree:
                    case approvalButtonConf.resubmit:
                        // 同意 重新提交
                        openApproveDialog.value = true;
                        approveDialogTitle.value = approvalButtonConf.buttonsObj[btnType];
                        break;
                    case approvalButtonConf.repulse:
                        // 退回
                        repulseDialogVisible.value = true;
                        break;
                }
            } else {
                proxy.$modal.msgError("请填写完整表单");
            }
        }).catch(() => {
            proxy.$modal.msgError("请填写完整表单");
        });
    } else {
        switch (btnType) {
            case approvalButtonConf.noAgree:
                // 不同意
                openApproveDialog.value = true;
                approveDialogTitle.value = approvalButtonConf.buttonsObj[btnType];
                break;
            case approvalButtonConf.transfer:
            case approvalButtonConf.addApproval:
                // 转办、加批
                dialogTitle.value = `设置${approvalButtonConf.buttonsObj[btnType]}人员`;
                if(btnType == approvalButtonConf.addApproval) {
                    dialogTitle.value = `${dialogTitle.value}（加批人审批完之后，会回到本节点的审批人再次审批）`;
                }
                addUserDialog();
                break;
            case approvalButtonConf.undertake:
                // 承办
                approveUndertakeSubmit();
                break;
        }
    }
}
/**
 * 审批操作确定
 * @param param 
 * @param type 
 */
const approveSubmit = async (param) => {
    approveSubData.approvalComment = param.remark;
    approveSubData.operationType = handleClickType.value;
    if (handleClickType.value == approvalButtonConf.resubmit || handleClickType.value == approvalButtonConf.agree) {
        await componentFormRef.value.handleValidate().then(async (isValid) => {
            if (isValid) {
                await componentFormRef.value.getFromData().then((data) => {
                    if (isLowCodeFlow && isLowCodeFlow == 'true') {
                        approveSubData.lfFields = JSON.parse(data); //低代码表单字段
                    } else {
                        let componentFormData = JSON.parse(data);
                        Object.assign(approveSubData, componentFormData);
                    }
                });
            }
        });
    };
    if (handleClickType.value == approvalButtonConf.repulse) {//退回操作
        approveSubData.backToModifyType = Number(param.backToModifyType);
        approveSubData.backToNodeId = Number(param.backToNodeId);
    }
    //console.log('approveSubData==========', JSON.stringify(approveSubData));
    await approveProcess(approveSubData);//业务处理
}
/**
 * 承办操作确定
 * @param param 
 * @param type 
 */
const approveUndertakeSubmit = async () => {
    approveSubData.approvalComment = "承办";
    approveSubData.operationType = handleClickType.value;
    proxy.$modal.confirm('确定完成操作吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(async () => {
        await processOperation(approveSubData).then((resData) => {
            if (resData.code == 200) {
                proxy.$modal.msgSuccess("承办成功");

            } else {
                proxy.$modal.msgError("承办失败:" + resData.errMsg);
            }

        }).then(() => {
            handleTabClick({ paneName: "baseTab" });
        });
    });
}
/**
 * 表单预览
 */
const preview = async () => {
    let queryParams = ref({
        formCode: formCode,
        processNumber: processNumber,
        type: 2,
        isOutSideAccessProc: isOutSideAccess,
        isLowCodeFlow: isLowCodeFlow
    });
    proxy.$modal.loading(); 
    await getViewBusinessProcess(queryParams.value).then(async (response) => {
        if (response.code == 200) {
            //显示审批按钮
            let auditButtons = response.data.processRecordInfo?.pcButtons?.audit;
            processTitle.value = response.data.processRecordInfo?.processTitle || '';
            if (Array.isArray(auditButtons) && auditButtons.length > 0) {
                approvalButtons.value = auditButtons.map(c => {
                    isResubmit.value = c.buttonType == approvalButtonConf.resubmit ? true : false;
                    return { value: c.buttonType, label: c.name };
                }).sort(function (a, b) {
                    return a.value - b.value
                });
                approvalButtons.value = uniqueByMap(approvalButtons.value);
            }
            if (isLowCodeFlow == 'true' || isOutSideAccess == 'true') {//低代码表单 和 外部表单接入
                lfFormDataConfig.value = response.data.lfFormData;
                lfFieldControlVOs.value = JSON.stringify(response.data.processRecordInfo.lfFieldControlVOs);
                lfFieldsConfig.value = JSON.stringify(response.data.lfFields);
                loadedComponent.value = await loadLFComponent();
                componentLoaded.value = true;  
                businessId.value = response.data.businessId;
            } else {//自定义表单
                componentData.value = response.data;
                loadedComponent.value = await loadDIYComponent(formCode);
                componentLoaded.value = true;
            }
        } else {
            proxy.$modal.msgError("获取表单数据失败:" + response.errMsg);
            close();
        }
        proxy.$modal.closeLoading();
    });
}
/**
 * 数组去重
 * @param arr 
 */
function uniqueByMap(arr) {
    if (!Array.isArray(arr)) {
        return []
    }
    const res = new Map();
    return arr.filter((item) => !res.has(item.value) && res.set(item.value, true) && item.label);
}

/**
 * 关闭当前审批页
 */
const close = () => {
    const path = proxy.$isMobile ? '/wecom/approveList' : '/flowTask/pendding'
    proxy.$tab.closeOpenPage({ path });
}
/**
 * 选人员Dialog 弹框
 */
const addUserDialog = () => {
    dialogVisible.value = true;
}
/**
 * 确定Dialog 弹框
 */
const sureDialogBtn = async (data) => {
    approveSubData.operationType = handleClickType.value;
    approveSubData.approvalComment = data.remark;
    if (!isMultiple.value) {
        data.selectList.unshift({
            id: getUserInfo('userId'),
            name: getUserInfo('userName'),
            ehrSource: getUserInfo('ehrSource'),
            controlDataType: 1
        });
        approveSubData.userInfos = data.selectList;
    } else {
        approveSubData.signUpUsers = data.selectList;
    }
    //console.log('sureDialogBtn==========approveSubData=============', JSON.stringify(approveSubData));  
    await approveProcess(approveSubData);
}
/**
 * 审批
 * @param param 
 */
const approveProcess = async (param) => {
    proxy.$modal.confirm('确定完成操作吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(async () => {
        dialogVisible.value = false;
        proxy.$modal.loading();
        await processOperation(param).then((res) => {
            if (res.code == 200) {
                proxy.$modal.msgSuccess("审批成功");
                close();
            } else {
                proxy.$modal.msgError("审批失败:" + res.errMsg);
            }
        });
        proxy.$modal.closeLoading();
    }).catch(() => { });

}

const handleTabClick = async (tab, event) => {
    activeName.value = tab.paneName;
    if (tab.paneName == 'baseTab') {
        preview();
    }
};
</script>
<style lang="scss" scoped>
.app-container {
    height: calc(100% - 20px);
    .card-box {
        height: 100%;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        .set-tabs {
            flex: 1;
            overflow: hidden;
            :deep(.el-tabs__content) {
                height: calc(100% - 65px);
                overflow: auto;
                .el-tab-pane {
                    height: 100%;
                    .el-table {
                        height: 100%;
                    }
                }
            }
            .approve {
                display: flex;
                flex-direction: column;
                height: 100%;
                overflow: hidden;
                .approve-buttons {
                    flex-shrink: 0;
                    background: white !important;
                    display: flex;
                    gap: 8px;
                    flex-wrap: wrap;
                    :deep(.el-button) {
                        margin: 0;
                    }
                }
                .component {
                    flex: 1;
                    background: white !important;
                    max-width: 720px !important;
                    overflow: auto;
                }
            }
        }
    }


    .my-form {
        max-width: 600px;
        min-height: 100px;
        margin: auto;
    }

    .el-timeline {
        --el-timeline-node-size-normal: 25px !important;
        --el-timeline-node-size-large: 25px !important;
    }

    .el-timeline-item__node--normal {
        left: -8px !important;
    }

    .el-timeline-item__node--large {
        left: -8px !important;
    }

    .el-timeline-item__wrapper {
        top: 0px !important;
    }
    .approve-title {
        flex-shrink: 0;
        display: flex;
        flex-wrap: wrap;
        gap: 3px;
    }
}
.isMobile {
    .app-container {
        height: 100%;
    }
}
</style>