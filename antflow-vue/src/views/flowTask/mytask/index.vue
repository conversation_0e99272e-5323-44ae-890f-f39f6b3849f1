<template>
   <div class="app-container">
      <div class="query-box">
         <el-form :model="taskMgmtVO" ref="queryRef" :inline="true" v-show="showSearch">
            <el-form-item label="审批编号" prop="businessId">
               <el-input v-model="taskMgmtVO.businessId" placeholder="请输入审批编号" clearable style="width: 200px"
                  @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="审批状态" prop="processState">
               <el-select v-model="taskMgmtVO.processState" placeholder="请选择审批状态" clearable style="width: 200px">
                  <el-option
                     v-for="item in pageData.statusOptions"
                     :key="item.value"
                     :label="item.label"
                     :value="item.value"
                  />
               </el-select>
            </el-form-item>
            <el-form-item label="模板名称" prop="templateName">
               <el-input v-model="taskMgmtVO.templateName" placeholder="请输入模板名称" clearable style="width: 200px"
                  @keyup.enter="handleQuery" />
            </el-form-item>
           <el-form-item label="创建时间" prop="createTime">
              <el-date-picker
                v-model="taskMgmtVO.createTime"
                type="datetimerange"
                :shortcuts="shortcuts"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                align="right">
              </el-date-picker>
           </el-form-item>
            <el-form-item>
               <el-button type="primary" @click="handleQuery">搜索</el-button>
               <el-button @click="resetQuery">重置</el-button>
            </el-form-item>
         </el-form>
         <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
               <el-button type="primary" plain @click="handleshowFlow">发起审批</el-button>
            </el-col>
         </el-row>
      </div>
      <div class="table-box">
         <el-table v-loading="loading" :data="dataList">
            <el-table-column label="审批编号" align="center" prop="businessId" show-overflow-tooltip />
            <el-table-column label="模板名称" align="center" prop="templateName" show-overflow-tooltip />
            <el-table-column label="模板分组" align="center" prop="templateGroupName" />
            <el-table-column label="状态" align="center" prop="processState" show-overflow-tooltip>
               <template #default="scope">
                  <span>{{ APPROVED_STATUS_MAP[scope.row.processState] }}</span>
               </template>
            </el-table-column>
            <el-table-column label="创建人" align="center" prop="createUser" show-overflow-tooltip />
            <el-table-column label="提交人" align="center" prop="submitUserName" />
            <el-table-column label="创建时间" align="center" prop="createTime" />
            <el-table-column label="更新时间" align="center" prop="updateTime" />
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
               <template #default="scope">
                  <el-button link type="primary" @click="handlePreview(scope.row)">查看</el-button>
                  <el-button :loading="scope.row.downloadPdfLoading" link type="primary" @click="handleDownloadPDF(scope.row)">下载审批单</el-button>
               </template>
            </el-table-column>
         </el-table>

         <pagination v-show="total > 0" :total="total" v-model:page="pageDto.page" v-model:limit="pageDto.pageSize"
            @pagination="getList" />
         <previewDrawer v-if="visible" />
      </div>
   </div>

</template>

<script setup>
import { getMyRequestlistPage, downloadPdf } from "@/api/workflow";
import previewDrawer from "@/views/workflow/components/previewDrawer.vue";
import { useStore } from '@/store/modules/workflow';
import { APPROVED_STATUS, APPROVED_STATUS_MAP } from '@/utils/constants';
const { proxy } = getCurrentInstance();
const router = useRouter();
let store = useStore();
let { setPreviewDrawer, setPreviewDrawerConfig } = store;
let previewDrawerVisible = computed(() => store.previewDrawer);
const dataList = ref([]);
const loading = ref(true);
const showSearch = ref(true);
const total = ref(0);

const shortcuts = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setDate(start.getDate() - 7)
      return [start, end]
    },
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setMonth(start.getMonth() - 1)
      return [start, end]
    },
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setMonth(start.getMonth() - 3)
      return [start, end]
    },
  },
]
let visible = computed({
   get() {
      return previewDrawerVisible.value
   },
   set() {
      closeDrawer()
   }
});

const pageData = reactive({
   form: {},
   pageDto: {
      page: 1,
      pageSize: 10
   },
   taskMgmtVO: {
      businessId: undefined,
      processTypeName: undefined,
      processState: null,
      createTime: ''
   },
   statusOptions: APPROVED_STATUS
});
const { pageDto, taskMgmtVO } = toRefs(pageData);

/** 查询岗位列表 */
async function getList() {
   let params = {
      ...taskMgmtVO.value
   }
   if(params.createTime?.length) {
      params.startCreateTime = params.createTime[0];
      params.endCreateTime = params.createTime[1];
   }
   delete params.createTime;

   loading.value = true;
   await getMyRequestlistPage(pageDto.value, params).then(response => {
      //console.log('response=========',JSON.stringify(response));
      dataList.value = response.data;
      total.value = response.pagination.totalCount;
   }).finally(() => {
      loading.value = false;
   })
}

/** 发起请求 */
function handleshowFlow() {
   router.push({ path: "/index" });
}
/** 搜索按钮操作 */
function handleQuery() {
   pageDto.value.page = 1;
   getList();
}
function resetQuery() {
   taskMgmtVO.value = {
      businessId: undefined,
      processTypeName: undefined,
      processState: null
   };
   handleQuery();
}

function handlePreview(row) {
   setPreviewDrawer(true);
   setPreviewDrawerConfig({
      formCode: row.processKey,
      processNumber: row.processNumber,
      isOutSideAccess: row.isOutSideProcess,
      isLowCodeFlow: row.isLowCodeFlow,
   })
}

// 下载pdf
const handleDownloadPDF = (row) => {
   let params = {
      formCode: row.processKey,
      processNumber: row.processNumber,
      type: 2,
      isOutSideAccessProc: false,
      isLowCodeFlow: true
   }
   row.downloadPdfLoading = true
   downloadPdf(params).then(res => {
      console.log(res);
      // 获取二进制数据
      const blob = res

      // 创建下载链接
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${row.description || '审批单'}.pdf`; // 设置下载文件名
      document.body.appendChild(a);
      a.click();

      // 清理
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
   }).finally(() => {
      row.downloadPdfLoading = false;
   })
}

getList();
</script>