import Cookies from 'js-cookie'
import cache from '@/plugins/cache';

const TokenKey = 'Admin-Token'
export function getToken() {
  return Cookies.get(TokenKey)
}

export function setToken(token) {
  return Cookies.set(TokenKey, token)
}

export function removeToken() {
  return Cookies.remove(TokenKey)
}

export function getUserInfo(key) {
  let userInfo = cache.local.get('userInfo');
  if (userInfo) {
    userInfo = JSON.parse(decodeURIComponent(userInfo));
  }
  if (key) {
    return userInfo?.[key] || '';
  }
  return userInfo;
}

export function setUserInfo(userInfo) {
  cache.local.set('userInfo', encodeURIComponent(JSON.stringify(userInfo)));
}

export function removeUserInfo() {
  cache.local.remove('userInfo');
}

// 超管：贲博jt92728、倪瑕jt36416、冯宇歆jt97017、杨亭jt106023
export const superEmpIds = ['jt92728', 'jt36416', 'jt97017', 'jt106023', 'jt87116']
export const isSuper =  (empId) => {
  return superEmpIds.includes(empId)
}

// 管理员：王昕jt48529、吴敏10851、王辰慧102652、侯晓瑶jt83326、解佳兴jt73183、宋慧媛2635、刘昕（HRBP）107670
export const adminEmpIds = ['jt48529', '10851', '102652', 'jt83326', 'jt73183', '2635', '107670']
export const isAdmin = (empId) => {
  return adminEmpIds.includes(empId) || isSuper(empId)
}