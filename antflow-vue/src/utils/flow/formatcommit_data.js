// import { FormatUtils } from '@/utils/flow/formatcommit_data'
//import { NodeUtils } from '@/utils/flow/nodeUtils'
const isEmpty = (data) =>
  data === null ||
  data === undefined ||
  data == "" ||
  data == "" ||
  data == "{}" ||
  data == "[]" ||
  data == "null";
const isEmptyArray = (data) => (Array.isArray(data) ? data.length === 0 : true);

export class FormatUtils {
  // 验证流程节点中涉及的表单信息
  static validProcessWithForm(basicData) {
    // 提取错误处理逻辑为独立函数
    function createError(node, message) {
      return {
        msg: `流程设计节点【${node.nodeName}】${message}`
      };
    }
    // 抽离字段控件同步逻辑为独立函数
    function syncFieldControls(node, formDataMap, formDataIds) {
      // 1. 过滤掉不存在的项
      node.lfFieldControlVOs = node.lfFieldControlVOs.filter(item => 
        formDataIds.has(item.fieldId)
      );

      // 获取第一个元素的公共属性
      const firstItem = node.lfFieldControlVOs[0];
      const commonProps = {
        formdataId: firstItem?.formdataId,
        nodeId: firstItem?.nodeId,
        perm: node.nodeType == 1 ? 'E' : 'R'
      };
      
      // 2. 添加新的项
      const existingIds = new Set(node.lfFieldControlVOs.map(item => item.fieldId));
      formDataIds.forEach(fieldId => {
        if (!existingIds.has(fieldId)) {
          const formItem = formDataMap.get(fieldId);
          node.lfFieldControlVOs.push({
            fieldId,
            fieldName: formItem?.options?.label,
            ...commonProps
          });
        }
      });
    }

    let err = null;
    basicData.err = err;
    let nodes = basicData.nodes;
    // 使用Map存储表单数据，提升查找效率
    const formData = JSON.parse(basicData.lfFormData || '{}')?.widgetList || [];
    let isSubmitterCount = 0, submitterLabel = '';
    for(const widget of formData) {
      if(['input', 'member'].includes(widget.type)) {
        if(widget.options.isSubmitter) {
          console.log('widget:', widget)
          isSubmitterCount++
          submitterLabel = submitterLabel ? `${submitterLabel}、${widget.options.label}` : widget.options.label
        }
      }
    }
    if(isSubmitterCount > 1) {
      err = {
        msg: `只能有一个表单项为提交人，当前【${submitterLabel}】表单项均为提交人，请检查处理`
      }
      basicData.err = err
      return basicData;
    }
    const formDataMap = new Map(formData.map(item => [item.id, item]));
    const formDataIds = new Set(formDataMap.keys());
    // 处理每个节点
    for (const node of nodes) {
      // 1. 检查关联成员控件
      if (node.nodeProperty == 100 && node.nodeRelatedControlId) {
        if (!formDataIds.has(node.nodeRelatedControlId)) {
          err = createError(node, `中【${node.nodeDisplayName}】的表单项不存在，请检查表单或流程设计`);
          break;
        }
      }
      
      // 2. 检查条件列表
      if (node.nodeType === 3 && Array.isArray(node.property?.conditionList)) {
        for (const condition of node.property.conditionList) {
          if (!formDataIds.has(condition.columnDbname)) {
            err = createError(node, `的条件中涉及的【${condition.showName}】表单项不存在，请检查表单或流程设计`);
            break;
          }
        }
        
        if (err) break; // 如果已发现错误，跳出外层循环
      }
      
      // 3. 同步lfFieldControlVOs与表单数据
      if (Array.isArray(node.lfFieldControlVOs) && node.lfFieldControlVOs.length > 0) {
        syncFieldControls(node, formDataMap, formDataIds);
      }
    }
    err && (basicData.err = err);
    return basicData;
  }
  /**
   * 对基础设置,高级设置等设置页内容进行格式化
   * @param params
   */
  static formatSettings(param) {
    let treeList = this.flattenMapTreeToList(param);
    let combinationList = this.getEndpointNodeId(treeList);
    let finalList = this.cleanNodeList(combinationList);
    let fomatList = this.adapterActivitiNodeList(finalList);
    return fomatList;
  }
  /**
   * 展平树结构
   * @param {Object} treeData  - 节点数据
   * @returns Array - 节点数组
   */
  static flattenMapTreeToList(treeData) {
    let nodeData = [];
    // nodeType：1发起人 2网关 3分支条件 4审核人 6抄送人 7并行审核
    function traverse(node) {
      if (!node && !node.hasOwnProperty("nodeType")) {
        return nodeData;
      }
      if (node.nodeType == 2) {
        if (node.childNode) {
          node.childNode.nodeFrom = node.nodeId;
          traverse(node.childNode);
        }
        if (!isEmptyArray(node.conditionNodes)) {
          for (let child of node.conditionNodes) {
            child.nodeFrom = node.nodeId;
            traverse(child);
          }
          node.nodeTo = node.conditionNodes.map((item) => item.nodeId);
          delete node.conditionNodes;
        }
      } else if (node.nodeType == 7) {
        if (node.childNode) {
          node.childNode.nodeFrom = node.nodeId;
          traverse(node.childNode);
        }
        if (!isEmptyArray(node.parallelNodes)) {
          for (let child of node.parallelNodes) {
            child.nodeFrom = node.nodeId;
            traverse(child);
          }
          node.nodeTo = node.parallelNodes.map((item) => item.nodeId);
          delete node.parallelNodes;
        }
      } else if (node.childNode) {
        node.nodeTo = [node.childNode.nodeId];
        node.childNode.nodeFrom = node.nodeId;
        traverse(node.childNode);
      }
      delete node.childNode;
      nodeData.push(node);
    }
    traverse(treeData);
    return nodeData;
  }
  /**
   * 过时已弃用
   */
  static obsolete_getEndpointNodeId(parmData) {
    if (isEmptyArray(parmData)) return parmData;

    let getwayList = parmData.filter((c) => {
      return c.nodeType == 2;
    });

    if (isEmptyArray(getwayList)) return parmData;

    let nodesGroup = {};
    for (let t of parmData) {
      if (nodesGroup.hasOwnProperty(t.nodeFrom)) {
        nodesGroup[t.nodeFrom].push(t);
      } else {
        nodesGroup[t.nodeFrom] = [t];
      }
    }
    for (let getway of getwayList) {
      if (nodesGroup.hasOwnProperty(getway.nodeId)) {
        let itemNodes = nodesGroup[getway.nodeId];
        let comNode = itemNodes.find((c) => {
          return c.nodeType != 3;
        });
        if (!comNode) continue;
        let conditionList = itemNodes.filter((c) => {
          return c.nodeId != comNode.nodeId;
        });
        for (let itemNode of conditionList) {
          function internalTraverse(info) {
            if (info) {
              if (!nodesGroup[info.nodeId]) {
                info.nodeTo = [comNode.nodeId];
              } else {
                let tempNode = nodesGroup[info.nodeId];
                if (Array.isArray(tempNode)) {
                  for (let t_item of tempNode) {
                    internalTraverse(t_item);
                  }
                } else {
                  internalTraverse(tempNode);
                }
              }
            }
          }
          internalTraverse(itemNode);
        }
      }
    }
    return parmData;
  }
  /**
   * 递归处理网关节点下属子节点的nodeTo数据
   * @param { Array } parmData -节点关系数组
   * @returns
   */
  static getEndpointNodeId(parmData) {
    if (isEmptyArray(parmData)) return parmData;
    let nodesGroup = {};
    for (let t of parmData) {
      if (isEmpty(t.nodeFrom)) continue;
      if (nodesGroup.hasOwnProperty(t.nodeFrom) && !isEmpty(t.nodeFrom)) {
        nodesGroup[t.nodeFrom].push(t);
      } else {
        nodesGroup[t.nodeFrom] = [t];
      }
    }
    //console.log("nodesGroup===========",JSON.stringify(nodesGroup));
    let parallelgetwayList = parmData.filter((c) => {
      return c.nodeType == 7; // 7并行审批
    });
    if (!isEmptyArray(parallelgetwayList)) {
      //处理并行审批网关
      for (let parallel of parallelgetwayList) {
        if (nodesGroup.hasOwnProperty(parallel.nodeId)) {
          let itemNodes = nodesGroup[parallel.nodeId];
          if (isEmptyArray(itemNodes)) continue;
          let childParallelList = itemNodes.filter((c) => {//并行子分支
            return parallel.nodeTo.includes(c.nodeId);
          });
          if (isEmptyArray(childParallelList)) continue;
          let parallelWayChild = itemNodes.find((c) => {//并行聚合节点
            return !parallel.nodeTo.includes(c.nodeId);
          });
          for (let itemNode of childParallelList) {
            function internalTraverse(info) {
              if (!info) return;
              if (info.nodeType == 7) {//并行审批嵌套
                let parallelCilds = nodesGroup[info.nodeId];
                let parallelComboNode = parallelCilds.find((c) => {//并行聚合节点递归
                  return !info.nodeTo.includes(c.nodeId);
                });
                internalTraverse(parallelComboNode);
              } else {
                if (
                  !nodesGroup[info.nodeId] &&
                  !isEmpty(parallelWayChild) &&
                  info.nodeId != parallelWayChild.nodeId
                ) {
                  info.nodeTo = [parallelWayChild.nodeId];
                } else {
                  let tempNode = nodesGroup[info.nodeId];
                  if (Array.isArray(tempNode)) {
                    for (let t_item of tempNode) {
                      internalTraverse(t_item);
                    }
                  } else {
                    internalTraverse(tempNode);
                  }
                }
              }
            }
            internalTraverse(itemNode);
          }
        }
      }
    }
    // 找到所有的网关节点
    let getwayList = parmData.filter((c) => {
      return c.nodeType == 2; // 2条件网关
    }).reverse();

    if (!isEmptyArray(getwayList)) {
      //处理条件网关
      for (let getway of getwayList) {
        if (nodesGroup.hasOwnProperty(getway.nodeId)) {
          let itemNodes = nodesGroup[getway.nodeId]; // 获取nodeFrom来自此网关的节点 即网关的第一层级所有子节点
          let comNode = itemNodes.find((c) => {
            return c.nodeType != 3; // 找到第一个不是条件节点的节点
          });
          if (!comNode) continue;
          let conditionList = itemNodes.filter((c) => {
            return c.nodeId != comNode.nodeId; // 找到所有条件节点
          });

          for (let itemNode of conditionList) { // 遍历所有条件节点
            function internalTraverse(info) {
              if (!info) return;
              if (info.nodeType == 7) {
                let condition_parallelNodes = nodesGroup[info.nodeId];
                if (isEmptyArray(condition_parallelNodes)) return;            
                let condition_parallelWayChild = condition_parallelNodes.find((c) => {//并行聚合节点
                  return !info.nodeTo.includes(c.nodeId);
                });
                condition_parallelWayChild.nodeTo = [comNode.nodeId];
                return; 
              };
              if (!nodesGroup[info.nodeId]) {
                info.nodeTo = [comNode.nodeId];
              } else {
                let tempNode = nodesGroup[info.nodeId];
                if (Array.isArray(tempNode)) {
                  for (let t_item of tempNode) {
                    internalTraverse(t_item);
                  }
                } else {
                  internalTraverse(tempNode);
                }
              }
            }
            internalTraverse(itemNode);
          }
        }
      }
    }
    return parmData;
  }

  /**
   * 清理节点数据
   * @param { Array } arr -节点数组
   * @returns
   */
  static cleanNodeList(arr) {
    let nodeIds = arr.map((c) => {
      return c.nodeId;
    });
    for (const node of arr) {
      node.nodeTo = Array.from(new Set(node.nodeTo));
      if (!isEmptyArray(node.nodeTo)) {
        node.nodeTo = node.nodeTo.filter((key) => {
          return nodeIds.indexOf(key) > -1;
        });
      }
    }
    return arr;
  }

  /**
   * 格式化node数据，对接api接口
   * @param {Array} nodeList
   * @returns
   */
  static adapterActivitiNodeList(nodeList) {
    for (let node of nodeList) {
      if (node.hasOwnProperty("id")) {
        delete node.id;
      }
      if (node.nodeType == 3) {
        let conditionObj = {
          conditionList: node.conditionList,
          sort: node.priorityLevel,
          isDefault: node.isDefault,
        };
        Object.assign(node, {
          property: {},
        });
        node.property = conditionObj;
        delete node.conditionList;
      }

      if (node.nodeType == 4 || node.nodeType == 6) {
        let approveObj = {
          emplIds: [],
          emplList: [],
          roleIds: [],
          roleList: [],
          hrbpConfType: 0,
          assignLevelGrade: 0,
          signType: node.signType,
          signUpType: 1,
          afterSignUpWay: 2,
        };

        if (node.nodeApproveList && !isEmptyArray(node.nodeApproveList)) {
          if (node.setType == 4) {
            for (let approve of node.nodeApproveList) {
              let role = {};
              role.id = approve.targetId || approve.id;
              role.name = approve.name;
              role.controlDataType = approve.controlDataType;
              role.ehrSource = approve.ehrSource;
              approveObj.roleIds.push(approve.targetId || approve.id);
              approveObj.roleList.push(role);
            }
          } else if (node.setType == 5) {
            console.log('node.nodeApproveList: ', node.nodeApproveList)
            for (let approve of node.nodeApproveList) {
              let emp = {};
              emp.id = approve.targetId || approve.id;
              emp.name = approve.name;
              emp.ehrSource = approve.ehrSource;
              emp.controlDataType = approve.controlDataType;
              approveObj.emplIds.push(approve.targetId || approve.id);
              approveObj.emplList.push(emp);
            }
          } else if (node.setType == 6) {
            for (let approve of node.nodeApproveList) {
              approveObj.hrbpConfType = approve.targetId || approve.id;
            }
          }
        } else if (node.setType == 3) {
          approveObj.assignLevelGrade = node.directorLevel;
        }
        approveObj.afterSignUpWay = node.property?.afterSignUpWay ?? 2;
        approveObj.signUpType = node.property?.signUpType ?? 1;
        node.nodeProperty = node.setType;
        node.nodeRelatedControlId = node.nodeRelatedControlId || null;
        node.property = approveObj;
        delete node.nodeApproveList;
      }
    }
    return nodeList;
  }
}
