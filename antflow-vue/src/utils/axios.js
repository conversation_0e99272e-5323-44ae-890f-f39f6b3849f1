/*
 * 接口请求拦截器
 */
import axios from "axios";
import router from '@/router'
import { ElMessage } from 'element-plus';
import { getToken, removeToken } from "@/utils/auth";
import useUserStore from '@/store/modules/user'

let config = {
  baseURL: window.__ce.BASEURL,
  timeout: 60 * 1000, // 超时时间
  headers: { 
    'X-Custom-Header': 'foobar',
    'Content-Type': 'application/json;charset=utf-8',
  }
};

const _axios = axios.create(config); 
_axios.interceptors.request.use(
  function (config) {
    const token = getToken();
    if (token && !config.url.includes('wechat/callback')) {
      if (!config.headers['Authorization']) {
        config.headers['Authorization'] = `Bearer ${token}`
      }
    }
    
    return config;
  },
  function (error) { 
    return Promise.reject(error);
  }
);
 
_axios.interceptors.response.use(
  function (response) {
    if(response.status === 200) {
      if(response.data.code === 200 || (response.config.url.includes('.json')) || (response.config.responseType === 'blob' && !response.data?.type?.includes('application/json'))) { // 最后两项用来判断返回数据为二进制流数据
        return response.data;
      } else if(response.data.errCode === '401') {
        // 登录过期
        useUserStore().logOut().finally(() => {
          ElMessage.error('登录已过期，请重新登录')
          const fullPath = router.currentRoute?.value?.fullPath
          if (fullPath) {
            router.push({
              path: '/login',
              query: {
                redirect: encodeURIComponent(fullPath)
              }
            })
          } else {
            router.push('/login')
          }
        })
        return Promise.reject(response.data);
      } else {
        ElMessage.error(response.data.errMsg || '请求失败，请稍后再试');
        return Promise.reject(response.data);
      }
    } else {
      ElMessage.error(response.data.errMsg || '请求失败，请稍后再试');
      return Promise.reject(response.data);
    }
  },
  function (error) {
    if(error?.response?.status == 403) {
      removeToken()
      const redirect = encodeURIComponent(location.pathname + location.search);
      let query = {};
      redirect && (query.redirect = redirect);
      
      router.push({
        path: '/login',
        query
      });
    } else {
      ElMessage.error(error?.message || '请求失败，请稍后再试');
    }
    return Promise.reject(error);
  }
);

export default _axios;
