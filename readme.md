## 二开主要修改点
> 本项目是基于来源的AntFlow审批流项目进行二次开发，原项目地址为: https://gitee.com/tylerzhou/Antflow.git
> 二次开发的目的是为了适配企微的审批流场景，因此在原项目的基础上做了一些修改。原项目bug较多，在原基础上做了一些bug修复

简要说明
AntFlow大部分的流程流转相关代码都在engine模块。除了 base、engine、web、等后端模块之外，还有一个没开源的antflow-common模块，这个模块的代码是没有放到开源仓库的。这部分的代码可以加作者QQ，跟作者要一下，一般都会给的。
由于一期开发过程中在开发自选抄送人的时候遇到一些问题，不得不对antflow-common这部分源码进行修改，因此会有一些自定义的类来替代掉原有的antflow-common相关的适配器类。

### 一、人员、组织相关

#### 1、人员、组织信息同步
> 业务背景：
> 审批系统的人员和组织信息要与企微的人员信息同步，同时还需要再同步的基础上做一些修改，详细修改如下
- 同步来源：ehr_db这个库的人员表(sync_ehr_employee_info)和组织表(sync_ehr_org_info)
- 同步频率：定时任务，每30分钟同步一次

#### 2、人员信息合并

### 二、模板、流程相关（核心）

#### 1、[新功能]审批单PDF打印
> A4纸打印、加对应公司主体的水印、包含基础信息、动态表单信息、审批记录信息。 其中动态表单信息和审批记录信息对于每个审批单和模板都是不一样的。

PDF导出目前留了两个版本：v1、导出与页面侧边栏的审批信息相同字段的审批单（也就是页面上审批发起人看到的是哪些字段，导出的就是哪些字段）v2、不限制隐藏字段，全部导出 。
因为需求变更，从v1改到了v2。

PDF相关代码：
- 导出的数据组装：org.openoa.engine.bpmnconf.service.impl.PdfServiceImpl.export
- 基于iText实现的导出A4模板的样式调整：org.openoa.base.util.PdfPrintReport.generatePDF

#### 2、审批记录
原项目中的审批记录错乱不堪，因此在原项目上改动了非常多。大部分改动都是为了兼容【会签】【顺序会签】【或签】【各种退回类型对应的情况】、【并签】等等。
`修改集中在：BpmVerifyInfoBizServiceImpl`。
审批记录分为：已审批过的记录、正在审批的审核信息、还未审批的审批信息。
有问题的地方都是在还未审批的审批信息中，原项目的做法是将模板中命中条件的审批节点列出来（除了转发节点，转发就是指的抄送），然后统一放出来，最后doAdd操作。
我们兼容的思路就是，在doAdd之后根据不同审批动作(会签或签等)的特点，进行filter对应的还未审批的审批信息。

#### 3、[新功能]：审批人不存在时，自动审批通过或转审
实现方案，已提PR：https://gitee.com/tylerzhou/Antflow/pulls/12

#### 4、[新功能]：自选抄送人
实现方案，已提PR：https://github.com/mrtylerzhou/AntFlow-activiti/pull/4

#### 5、[目前提交模板的时候，默认是后去重]：后去重兼容
去重类型保存在了t_bpmn_conf的deduplication_type，前端会将deduplicationType默认置为后去重，并在页面上不会体现

#### 6、企微消息通知




