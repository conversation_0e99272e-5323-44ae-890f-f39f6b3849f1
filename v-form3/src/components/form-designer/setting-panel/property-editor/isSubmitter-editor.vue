<template>
  <el-form-item :label="i18nt('designer.setting.isSubmitter')">
    <el-radio-group v-model="optionModel.isSubmitter" class="radio-group-custom">
      <el-radio :value="true">是</el-radio>
      <el-radio :value="false">否</el-radio>
    </el-radio-group>
  </el-form-item>
</template>

<script>
  import i18n from "@/utils/i18n"

  export default {
    name: "isSubmitter-editor",
    mixins: [i18n],
    props: {
      designer: Object,
      selectedWidget: Object,
      optionModel: Object,
    }
  }
</script>