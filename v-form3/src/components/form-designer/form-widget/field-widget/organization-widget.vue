<template>
  <form-item-wrapper :designer="designer" :field="field" :rules="rules" :design-state="designState"
                     :parent-widget="parentWidget" :parent-list="parentList" :index-of-parent-list="indexOfParentList"
                     :sub-form-row-index="subFormRowIndex" :sub-form-col-index="subFormColIndex" :sub-form-row-id="subFormRowId">
    <el-tree-select
      class="full-width-input"
      ref="fieldEditor" :data="field.options.optionItems" v-model="fieldModel"
      :key="field.id"
      :disabled="field.options.disabled"
      :size="widgetSize"
      :multiple="field.options.multiple"
      :clearable="field.options.clearable"
      :check-strictly="field.options.checkStrictly"
      :placeholder="field.options.placeholder || i18nt('render.hint.selectPlaceholder')"
      :props="{ isLeaf: 'isLeaf' }"
      @focus="handleFocusCustomEvent" @blur="handleBlurCustomEvent"
      @change="handleChangeEvent"
      node-key="nodeKey"
      :loading="optionLoading"
      :load="loadNode"
      @node-click="handleNodeClick"
      @clear="handleClear"
      lazy
      :filterable="field.options.filterable"
      remote
      :remote-method="remoteMethod"
      :default-expanded-keys="expandedKeys"
    >
      <template #default="{ node, data }">
        <div class="custom-tree-node">
          <span v-if="showQueryList && data.namePath" class="tree-node-label">{{ formattedNamePath(data.namePath, 16) }}：</span>
          <span class="tree-node-label">{{ node.label }}</span>
        </div>
      </template>
      <template v-if="field.options.multiple" #tag>
        <div class="select-tag">
          <el-tag v-for="item in allFieldModel" :key="`${item.ehrSource}_${item.id}`" :closable="!field.options.disabled" type="info" @close="handleRemove(item)" class="ellipsis-tag">
            <span v-if="showFullPath" class="tag-text">{{ item.namePath ? `${item.namePath}/${item.name}` : item.name }}</span>
            <span v-else class="tag-text">{{ item.name }}</span>
          </el-tag>
        </div>
      </template>
      <template v-else #label>
        <div class="select-tag">
          <el-tag v-for="item in allFieldModel" :key="`${item.ehrSource}_${item.id}`" :closable="!field.options.disabled" type="info" @close="handleRemove(item)" class="ellipsis-tag">
            <span v-if="showFullPath" class="tag-text">{{ item.namePath ? `${item.namePath}/${item.name}` : item.name }}</span>
            <span v-else class="tag-text">{{ item.name }}</span>
          </el-tag>
        </div>
      </template>
    </el-tree-select>
  </form-item-wrapper>
</template>


<script>
  import FormItemWrapper from './form-item-wrapper'
  import emitter from '@/utils/emitter'
  import i18n, {translate} from "@/utils/i18n";
  import fieldMixin from "@/components/form-designer/form-widget/field-widget/fieldMixin";
  import { EHR_SOURCE } from '@/utils/constants'
  import { queryByNameFuzzy, getOrgTree } from '@/api/label'
  import { addNodeKey, formattedNamePath } from '@/utils/util'

  export default {
    name: "organization-widget",
    componentName: 'FieldWidget',  //必须固定为FieldWidget，用于接收父级组件的broadcast事件
    mixins: [emitter, fieldMixin, i18n],
    props: {
      field: Object,
      parentWidget: Object,
      parentList: Array,
      indexOfParentList: Number,
      designer: Object,

      designState: {
        type: Boolean,
        default: false
      },

      subFormRowIndex: { /* 子表单组件行索引，从0开始计数 */
        type: Number,
        default: -1
      },
      subFormColIndex: { /* 子表单组件列索引，从0开始计数 */
        type: Number,
        default: -1
      },
      subFormRowId: { /* 子表单组件行Id，唯一id且不可变 */
        type: String,
        default: ''
      }
    },
    components: {
      FormItemWrapper
    },
    data() {
      return {
        oldFieldValue: null, //field组件change之前的值
        fieldModel: null,
        allFieldModel: [],
        rules: [],
        optionLoading: false,
        showQueryList: false,
        expandedKeys: [], // 展开的节点keys
      }
    },
    computed: {
      showFullPath() {
        return (this.field.options.showAllLevels === undefined) || !!this.field.options.showAllLevels
      }
    },
    beforeCreate() {
      /* 这里不能访问方法和属性！！ */
    },

    created() {
      /* 注意：子组件mounted在父组件created之后、父组件mounted之前触发，故子组件mounted需要用到的prop
         需要在父组件created中初始化！！ */
      this.field.options.optionItems = addNodeKey(EHR_SOURCE, this.field.id)
      this.allFieldModel = []
      this.fieldModel = this.field.options.multiple ? [] : ''
      this.initOptionItems()
      this.initFieldModel()
      this.registerToRefList()
      this.initEventHandler()
      this.buildFieldRules()

      this.handleOnCreated()
    },

    mounted() {
      this.setTreeChecked()
      this.handleOnMounted();
      this.field.options.fieldTypeName = "organization";
      this.field.options.fieldType = "1";
    },

    beforeUnmount() {
      this.unregisterFromRefList()
    },

    methods: {
      // 格式化namePath
      formattedNamePath,
      // 树节点点击
      handleNodeClick (node, nodeProp) {
        console.log('handleNodeClick: ', node, nodeProp, this.fieldModel, this.allFieldModel)
        if(!this.field.options.checkStrictly && !node.isLeaf) { // 非任意节点可选时，只能选叶子节点
          return
        }
        let empId = node.value
        let empIds = this.allFieldModel.map(item => item.id)
        const isChecked = nodeProp.checked = !nodeProp.checked;
        const isExisting = empIds.includes(empId);
        let ehrSource = ''
        let namePath = nodeProp.data.namePath || ''
        if(nodeProp.level === 1){
          ehrSource = nodeProp.data.ehrSource
          namePath = nodeProp.data.namePath || nodeProp.data.label
        } else {
          let parent = nodeProp.parent
          while(parent && parent.level !== 1){
            if(!nodeProp.data.namePath) {
              namePath = namePath ? parent?.data?.label + '/' + namePath : parent?.data?.label
            }
            parent = parent.parent;
          }
          ehrSource = parent?.data?.ehrSource || '';
          if(!nodeProp.data.namePath) {
            namePath = namePath ? parent?.data?.label + '/' + namePath : parent?.data?.label
          }
        }
        if(isChecked && !isExisting) { // 选中 但不存在
          let currentItem = {
            ehrSource,
            namePath,
            nodeKey: node.nodeKey || `${this.field.id}_${empId}`,
            controlDataType: node.type,
            id: empId,
            name: node.label
          }
          if(this.field.options.multiple) {
            this.allFieldModel.push(currentItem)
          } else {
            this.allFieldModel = [currentItem]
          }
        } else if(!isChecked && isExisting) { // 取消选中 但存在
          this.allFieldModel = this.allFieldModel.filter(item => (item.ehrSource != ehrSource) || (item.ehrSource == ehrSource && item.id != empId))
        }
        this.setTreeChecked()
      },
      // 删除标签
      handleRemove(item) {
        // 移除选中 返回ehrSource不同 或者 ehrSource相同但id不同
        this.allFieldModel = this.allFieldModel.filter(el => (item.ehrSource != el.ehrSource) || (item.ehrSource == el.ehrSource && item.id != el.id))
        this.setTreeChecked()
        this.handleChangeEvent(this.allFieldModel)
      },
      handleClear() {
        this.allFieldModel = []
        this.setTreeChecked()
        this.handleChangeEvent(this.allFieldModel)
      },

      // 懒加载
      async loadNode(node, resolve, reject) {
        console.log('organization-loadNode: ', node)
        if (!node || node.level === 0) {
          let treeList = this.field?.options?.optionItems || EHR_SOURCE || []
          return resolve(treeList)
        }
        if (node.level === 1 && !node.data.isLeaf) {
          try {
            let res = await this.getTreeList(node.data.ehrSource)
            // 如果某个节点没有 children，手动标记为 leaf: true
            const processedData = res.map(item => ({
              ...item,
              isLeaf: !item.children || item.children.length === 0,
            }));
            this.setTreeChecked()
            return resolve(processedData);
          } catch (error) {
            return reject(error);
          }
        }
        // 如果已经是最后一级，返回空数组并标记 leaf: true
        if (!node.data.children || node.data.children.length === 0) {
          return resolve([]);
        }
        // 否则返回子节点，并检查是否需要标记 leaf
        node.data.children.forEach(child => {
          child.isLeaf = !child.children || child.children.length === 0
        })
        return resolve(node.data.children);
      },
      // 获取树列表
      async getTreeList(ehrSource) {
        let type = 2
        let allTreeList = sessionStorage.getItem(`allTreeList_${type}`)
        allTreeList = allTreeList ? JSON.parse(allTreeList) : {}
        if(allTreeList[ehrSource]?.length) {
          let childData = addNodeKey(allTreeList[ehrSource], this.field.id)
          this.field.options.optionItems.forEach(item => {
            if(item.ehrSource == ehrSource) {
              item.children = childData
            }
          })
          return childData
        }
        let res = await getOrgTree({ ehrSource, type })
        allTreeList[ehrSource] = res?.data?.children || []
        let childData = addNodeKey(allTreeList[ehrSource], this.field.id)
        this.field.options.optionItems.forEach(item => {
          if(item.ehrSource == ehrSource) {
            item.children = childData
          }
        })
        sessionStorage.setItem(`allTreeList_${type}`, JSON.stringify(allTreeList))
        return childData
      },

      // 远程获取搜索节点数据
      remoteMethod(query) {
        if (query) {
          this.fetchQueryData(query)
        } else {
          this.showQueryList = false
          let type = 2
          let allTreeList = sessionStorage.getItem(`allTreeList_${type}`)
          allTreeList = allTreeList ? JSON.parse(allTreeList) : {}

          this.field.options.optionItems = addNodeKey(EHR_SOURCE, this.field.id).map(item => {
            let childData = allTreeList[item.ehrSource]?.length ? addNodeKey(allTreeList[item.ehrSource], this.field.id) : null
            item.children = childData
            return item
          })
          // treeList更换时重新设置选中
          this.setTreeChecked()
        }
      },
      // 模糊查询
      fetchQueryData(query) {
        let params = {
          name: query.trim(),
          queryType: 2
        }
        this.optionLoading = true
        queryByNameFuzzy(params).then(res => {
          this.field.options.optionItems =(res?.data?.orgs || []).map(item => ({
            ...item,
            label: item.name,
            ehrSource: item.ehrSource,
            value: item.id,
            nodeKey: `${this.field.id}_${item.id}`,
            isLeaf: true
          }))
          // treeList更换时重新设置选中
          this.setTreeChecked()
        }).finally(() => {
          this.showQueryList = true
          this.optionLoading = false
        })
      },
      // 获取ehrSource的label
      getEhrSourceLabel(ehrSource) {
        return EHR_SOURCE.find(item => item.ehrSource == ehrSource)?.label
      },
      // 设置树选中
      setTreeChecked() {
        this.$nextTick(() => {
          const editor = this.getFieldEditor();
          if (!editor) return;
          this.allFieldModel = this.allFieldModel || []
          if(this.field.options.multiple) {
            this.fieldModel = this.allFieldModel.map(item => `${this.field.id}_${item.id}`)
            editor.setCheckedKeys(this.fieldModel)
          } else {
            let itemId = this.allFieldModel[0]?.id || ''
            this.fieldModel = itemId ? `${this.field.id}_${itemId}` : ''
            editor.setCheckedKeys(this.fieldModel ? [this.fieldModel] : [])
          }
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  .full-width-input {
    width: 100% !important;
  }
  .select-tag {
    max-width: 100%;
    box-sizing: border-box;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    .ellipsis-tag {
      height: auto;
      max-width: 100%;
      box-sizing: border-box;
      line-height: 1.5;
      padding: 3px 5px;
      .tag-text {
        white-space: break-spaces;
      }
    }
  }
</style>