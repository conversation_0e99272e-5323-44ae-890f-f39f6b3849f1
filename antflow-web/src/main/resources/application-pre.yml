server: 
  port: 7001
spring: 
  datasource:
    url: *******************************************************************
    username: root
    password: 12345678
    driver-class-name: com.mysql.cj.jdbc.Driver
    druid: 
      min-idle: 5
      initial-size: 5
      keep-alive: true
      max-wait: 60000
      max-active: 100
      removeAbandoned: true
      removeAbandonedTimeout: 1800
      logAbandoned: true
      validation-query: SELECT 1 FROM DUAL
      validation-query-timeout: 2000
      test-on-borrow: false
      test-on-return: false
      test-while-idle: true
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
    hikari: 
      max-lifetime: 120000
  activiti: 
    # logging.level.org.openoa.engine.conf.mvc.JiMuMDCCommonsRequestLoggingFilter=debug
    # activiti Disable Auto Table Creation
    database-schema-update: none
  redis:
    client-type: lettuce
    cluster:
      max-redirects: 8
      nodes: 10.24.49.249:7000,10.24.49.249:7001,10.24.49.249:7002
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        max-wait: -1
        min-idle: 0
    password: epRedis@019
    timeout: 5000
mybatis: 
  configuration: 
    ## mybatis
    map-underscore-to-camel-case: true
  type-aliases-package: org.openoa.**.entity
#mybatis-plus:
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
logging: 
  level: 
    org: 
      openoa: 
        mapper: info
      activiti: 
        engine: 
          impl: 
            persistence: 
              entity: info

xxl:
  job:
    accessToken:
    admin:
      addresses: http://pre-omo.aiyouyi.cn/xxl-job-admin/
    executor:
      appname: antflow-pre
      address:
      ip:
      port: 9421
      logpath: logs/xxl-job/jobhandler
      logretentiondays: 3
      oneTimesJob:
        timeout: 10000
file:
  upload:
    dir: /data/share/www/offline/upload
    url: https://test-cesupport-images.ceboss.cn/upload
gateway:
  flowApi:
    url: https://test-cescrm.ceboss.cn/antflow
master-data:
  domain: http://************:8080

wechat:
  chenxing:
    agentId: 1000099
    corpId: wwe2351ed80f0479d4
    corpsecret: _PhZTbZXs6-zucZq1efQXWW5L3ya69RUkTgQRj0eWQk
    approveDomain: test-approve.oristarcloud.com
  jituan:
    agentId: 1000008
    corpId: wwa0a360c6341c5ae6
    corpsecret: GnyB1Y1-SWkutF8_vIDeyGj3WsYqzuDTo9eB5kzJbOg
    approveDomain: test-approve.ce-group.cn
  kuajing:
    agentId: 1000060
    corpId: ww8bd429f5b618a57e
    corpsecret: SFO9FKx4G0Yl_y5E8Jw5cf0DJHmuVisRsOQouayXYkM
    approveDomain: test-approve.gboss.tech
  smhg:
    agentId: 999999
    corpId: 999999
    corpsecret: 999999
    approveDomain: 999999
  xiaoxia:
    agentId: 1000011
    corpId: ww71746e109858902c
    corpsecret: jAYDpVHoRihTuXLJ8Zm1HPV5M1t9XMi7-861mGdl4rA
    approveDomain: test-approve.xiaoxiatech.com
  xinwang:
    agentId: 1000129
    corpId: wwd674e2ffdc4f6862
    corpsecret: kG1XfR6fwFoPBRBIPev92WVw5QtOu2UB0AiNx0TluG4
    approveDomain: test-approve.xinnet.com
  zhongqi:
    agentId: 1000229
    corpId: wwf1c477c862181a7f
    corpsecret: IH5PGBOHD1dOMX86TOHi8UclM7uUUzaODKvbmE2m-ZA
    approveDomain: test-approve.ceboss.cn
#alarmAddress: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=f16982db-cd04-4aca-adac-8a8ff5809bb3
wx_redirect_url: https://open.weixin.qq.com/connect/oauth2/authorize?appid={corpId}&redirect_uri={redirectUrl}&response_type=code&state=approvalProcess&scope=snsapi_base#wechat_redirect