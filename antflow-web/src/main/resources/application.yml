spring:
  application:
    name: antflow
  profiles:
    active: pre
  activiti:
    check-process-definitions: false
    database-schema-update: none
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      write-dates-as-timestamps: false
  aop:
    proxy-target-class: true
  main:
    allow-circular-references: true
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
mybatis:
  config-location: classpath:mybatis-config.xml
antflow:
  common:
    empTable:
      empTblName: t_user
      idField: id
      nameField: user_name
    scan-packages: org.openoa,com.package
message:
  email:
    host: smtp.163.com
    account: <EMAIL>
    password: HHVZDETFJMCATUGS
knife4j:
  enable: true
  setting:
    footer-custom-content: <div style='color:red'>AntFlow?????</div>
    enable-dynamic-parameter: true
    enable-version: true
    enable-filter-multipart-apis: true
  cors: false
  production: false
