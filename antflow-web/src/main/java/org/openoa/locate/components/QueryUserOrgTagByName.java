package org.openoa.locate.components;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.openoa.base.entity.SyncEhrOrgInfo;
import org.openoa.base.service.AfUserService;
import org.openoa.base.service.SyncEhrOrgInfoService;
import org.openoa.base.vo.OrgEmpTagInfoVo;
import org.openoa.common.enums.QueryUserOrgTagTypeEnum;
import org.openoa.locate.factory.QueryUserDynamicCalculate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2025-05-27 18:21
 */
@Component(QueryUserOrgTagTypeEnum.TypeConstants.userOrgTag)
public class QueryUserOrgTagByName implements QueryUserDynamicCalculate {

	@Resource
	private AfUserService userService;
	@Resource
	private SyncEhrOrgInfoService syncEhrOrgInfoService;

	@Override
	public Map<String, List<OrgEmpTagInfoVo>> locateQuery(String name) {
		List<OrgEmpTagInfoVo> users =  userService.queryUserByNameFuzzy(name);
		if (!CollectionUtils.isEmpty(users)) {
			List<SyncEhrOrgInfo> orgInfos = syncEhrOrgInfoService.list(new LambdaQueryWrapper<SyncEhrOrgInfo>().in(SyncEhrOrgInfo::getOrgId, Lists.transform(users, OrgEmpTagInfoVo::getOrgIdFromUser)));
			if (!CollectionUtils.isEmpty(orgInfos)) {
				Map<String, String> namePathMap = userService.getNamePath(orgInfos, false);
				users.forEach(user -> {
					String orgId = user.getOrgIdFromUser();
					String ehrSource = user.getEhrSource();
					if (StringUtils.isNotBlank(orgId) && StringUtils.isNotBlank(ehrSource)) {
						String key = ehrSource + ":" + orgId;
						String namePath = namePathMap.getOrDefault(key, "");
						user.setNamePath(namePath);
					}
				});
			}
		}
		List<OrgEmpTagInfoVo> tags =  userService.queryTagByNameFuzzy(name);
		List<OrgEmpTagInfoVo> orgs =  userService.queryOrgByNameFuzzy(name);
		List<OrgEmpTagInfoVo> userList = modifyFirstOrgNameToEhrSourceName(users);
		List<OrgEmpTagInfoVo> orgList = modifyFirstOrgNameToEhrSourceName(orgs);
		return new HashMap<String, List<OrgEmpTagInfoVo>>() {
			{
				put("users", userList);
				put("tags", tags);
				put("orgs", orgList);
			}
		};
	}

}
