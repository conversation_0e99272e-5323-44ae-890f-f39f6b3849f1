package org.openoa.locate.components;

import org.openoa.base.service.AfUserService;
import org.openoa.base.service.SyncEhrOrgInfoService;
import org.openoa.base.vo.OrgEmpTagInfoVo;
import org.openoa.common.enums.QueryUserOrgTagTypeEnum;
import org.openoa.locate.factory.QueryUserDynamicCalculate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2025-05-27 18:21
 */
@Component(QueryUserOrgTagTypeEnum.TypeConstants.user)
public class QueryUserByName implements QueryUserDynamicCalculate {

	@Resource
	private AfUserService userService;
	@Resource
	private SyncEhrOrgInfoService syncEhrOrgInfoService;

	@Override
	public Map<String, List<OrgEmpTagInfoVo>> locateQuery(String name) {
		List<OrgEmpTagInfoVo> orgEmpTagInfoVos = userService.queryUserByNameFuzzy(name);
		List<OrgEmpTagInfoVo> orgEmpTagInfoVoList = modifyFirstOrgNameToEhrSourceName(orgEmpTagInfoVos);

		return new HashMap<String, List<OrgEmpTagInfoVo>>() {
			{
				put("users", orgEmpTagInfoVoList);
			}
		};
	}

}
