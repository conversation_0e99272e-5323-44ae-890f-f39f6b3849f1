2025-08-27 13:45:05.994 INFO  [main] org.springframework.boot.StartupInfoLogger.logStarting:55 - [traceId:] Starting AntFlowApplication using Java 1.8.0_432 on KyleHsudeMacBook-Pro.local with PID 42231 (/Users/<USER>/shuma/source-code/Antflow/antflow-web/target/classes started by kylehsu in /Users/<USER>/shuma/source-code/Antflow)
2025-08-27 13:45:05.995 INFO  [main] org.springframework.boot.SpringApplication.logStartupProfileInfo:638 - [traceId:] The following 1 profile is active: "pre"
2025-08-27 13:45:06.674 INFO  [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate.multipleStoresDetected:262 - [traceId:] Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-27 13:45:06.675 INFO  [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate.registerRepositoriesIn:132 - [traceId:] Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-27 13:45:06.771 INFO  [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate.registerRepositoriesIn:201 - [traceId:] Finished Spring Data repository scanning in 65 ms. Found 0 Redis repository interfaces.
2025-08-27 13:45:07.373 INFO  [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize:108 - [traceId:] Tomcat initialized with port(s): 7001 (http)
2025-08-27 13:45:07.378 INFO  [main] org.apache.juli.logging.DirectJDKLog.log:173 - [traceId:] Starting service [Tomcat]
2025-08-27 13:45:07.378 INFO  [main] org.apache.juli.logging.DirectJDKLog.log:173 - [traceId:] Starting Servlet engine: [Apache Tomcat/9.0.82]
2025-08-27 13:45:07.427 INFO  [main] org.apache.juli.logging.DirectJDKLog.log:173 - [traceId:] Initializing Spring embedded WebApplicationContext
2025-08-27 13:45:07.427 INFO  [main] org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.prepareWebApplicationContext:292 - [traceId:] Root WebApplicationContext: initialization completed in 1318 ms
2025-08-27 13:45:08.110 INFO  [main] com.zaxxer.hikari.HikariDataSource.getConnection:110 - [traceId:] HikariPool-1 - Starting...
2025-08-27 13:45:08.893 INFO  [main] com.zaxxer.hikari.HikariDataSource.getConnection:123 - [traceId:] HikariPool-1 - Start completed.
2025-08-27 13:45:09.152 INFO  [main] org.activiti.engine.impl.ProcessEngineImpl.<init>:86 - [traceId:] ProcessEngine default created
2025-08-27 13:45:09.153 INFO  [main] org.openoa.engine.bpmnconf.activitilistener.BpmnGlobalEventListener.onEvent:45 - [traceId:] AntFlow Engine is started
2025-08-27 13:45:09.153 INFO  [main] org.activiti.engine.impl.cfg.multitenant.MultiSchemaMultiTenantProcessEngineConfiguration.createTenantSchema:160 - [traceId:] creating/validating database schema for tenant 
2025-08-27 13:45:11.807 INFO  [main] org.openoa.base.dto.wecom.WechatConfigProperties.init:40 - [traceId:] ===> wechat config: WechatConfigProperties(zhongqi=WechatConfigProperties.WechatConfig(agentId=1000229, corpId=wwf1c477c862181a7f, corpsecret=IH5PGBOHD1dOMX86TOHi8UclM7uUUzaODKvbmE2m-ZA, approveDomain=test-approve.ceboss.cn), chenxing=WechatConfigProperties.WechatConfig(agentId=1000099, corpId=wwe2351ed80f0479d4, corpsecret=_PhZTbZXs6-zucZq1efQXWW5L3ya69RUkTgQRj0eWQk, approveDomain=test-approve.oristarcloud.com), xinwang=WechatConfigProperties.WechatConfig(agentId=1000129, corpId=wwd674e2ffdc4f6862, corpsecret=kG1XfR6fwFoPBRBIPev92WVw5QtOu2UB0AiNx0TluG4, approveDomain=test-approve.xinnet.com), kuajing=WechatConfigProperties.WechatConfig(agentId=1000060, corpId=ww8bd429f5b618a57e, corpsecret=SFO9FKx4G0Yl_y5E8Jw5cf0DJHmuVisRsOQouayXYkM, approveDomain=test-approve.gboss.tech), jituan=WechatConfigProperties.WechatConfig(agentId=1000008, corpId=wwa0a360c6341c5ae6, corpsecret=GnyB1Y1-SWkutF8_vIDeyGj3WsYqzuDTo9eB5kzJbOg, approveDomain=test-approve.ce-group.cn), smhg=WechatConfigProperties.WechatConfig(agentId=999999, corpId=999999, corpsecret=999999, approveDomain=999999), xiaoxia=WechatConfigProperties.WechatConfig(agentId=1000011, corpId=ww71746e109858902c, corpsecret=jAYDpVHoRihTuXLJ8Zm1HPV5M1t9XMi7-861mGdl4rA, approveDomain=test-approve.xiaoxiatech.com))
2025-08-27 13:45:12.755 INFO  [main] cn.ce.xxljob.config.XxlAdminAddressConfig.afterPropertiesSet:91 - [traceId:] 处理配置项: xxl.job.admin.addresses:http://pre-omo.aiyouyi.cn/xxl-job-admin/
2025-08-27 13:45:12.755 INFO  [main] cn.ce.xxljob.config.XxlAdminAddressConfig.afterPropertiesSet:109 - [traceId:] Xxl Job Master Admin URL: http://pre-omo.aiyouyi.cn/xxl-job-admin/
2025-08-27 13:45:12.757 INFO  [main] cn.ce.xxljob.config.XxlJobConfig.xxlJobExecutor:46 - [traceId:] >>>>>>>>>>> xxl-job config init.
2025-08-27 13:45:13.097 INFO  [main] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:168 - [traceId:] >>>>>>>>>>> xxl-job register jobhandler success, name:ANTFLOW_SYNC_EHR_EMP_INFO_JOB_HANDLER, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@3e149513[class org.openoa.job.SyncEhrDataJob#syncEhrEmpInfoJobHandler]
2025-08-27 13:45:13.097 INFO  [main] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:168 - [traceId:] >>>>>>>>>>> xxl-job register jobhandler success, name:ANTFLOW_SYNC_EHR_ORG_INFO_JOB_HANDLER, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@787a4519[class org.openoa.job.SyncEhrDataJob#syncEhrOrgInfoJobHandler]
2025-08-27 13:45:13.200 INFO  [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer.start:220 - [traceId:] Tomcat started on port(s): 7001 (http) with context path ''
2025-08-27 13:45:13.223 INFO  [Thread-20] com.xxl.job.core.server.EmbedServer$1.run:86 - [traceId:] >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9421
2025-08-27 13:45:14.566 INFO  [main] org.springframework.boot.StartupInfoLogger.logStarted:61 - [traceId:] Started AntFlowApplication in 9.059 seconds (JVM running for 9.491)
2025-08-27 13:45:14.568 INFO  [main] org.openoa.common.config.StartedListener.onApplicationEvent:25 - [traceId:] [94myour project server started at: http://127.0.0.1:7001[0;39m
2025-08-27 13:45:14.568 INFO  [main] org.openoa.common.config.StartedListener.onApplicationEvent:27 - [traceId:] [93myour project server was started successfully on port:7001 with context path:/[0;39m
2025-08-27 13:45:14.569 ERROR [main] org.openoa.AntFlowApplication.main:15 - [traceId:] AntFlow 项目启动成功!
2025-08-27 13:45:14.596 INFO  [pool-4-thread-1] com.nrcp.log.ErrorLogSender$1.run:100 - [traceId:] ErrorLogSender send error log [pre 环境antflow项目 TraceId: null ip: ************** Exception: AntFlow 项目启动成功! argsList: null]
2025-08-27 13:45:15.223 INFO  [pool-4-thread-1] com.nrcp.log.CompanyWeChatAlarmService.sendAlarm:50 - [traceId:] alarm call wechat roobat response info [{"errcode":0,"errmsg":"ok"}]
2025-08-27 13:47:10.907 INFO  [Thread-20] com.xxl.job.core.server.EmbedServer$1.run:96 - [traceId:] >>>>>>>>>>> xxl-job remoting server stop.
2025-08-27 13:47:11.007 INFO  [xxl-job, executor ExecutorRegistryThread] com.xxl.job.core.thread.ExecutorRegistryThread$1.run:87 - [traceId:] >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='antflow-pre', registryValue='http://**************:9421/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-08-27 13:47:11.008 INFO  [xxl-job, executor ExecutorRegistryThread] com.xxl.job.core.thread.ExecutorRegistryThread$1.run:105 - [traceId:] >>>>>>>>>>> xxl-job, executor registry thread destory.
2025-08-27 13:47:11.008 INFO  [SpringApplicationShutdownHook] com.xxl.job.core.server.EmbedServer.stop:125 - [traceId:] >>>>>>>>>>> xxl-job remoting server destroy success.
2025-08-27 13:47:11.008 INFO  [xxl-job, executor JobLogFileCleanThread] com.xxl.job.core.thread.JobLogFileCleanThread$1.run:99 - [traceId:] >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2025-08-27 13:47:11.009 INFO  [xxl-job, executor TriggerCallbackThread] com.xxl.job.core.thread.TriggerCallbackThread$1.run:97 - [traceId:] >>>>>>>>>>> xxl-job, executor callback thread destory.
2025-08-27 13:47:11.009 INFO  [Thread-19] com.xxl.job.core.thread.TriggerCallbackThread$2.run:127 - [traceId:] >>>>>>>>>>> xxl-job, executor retry callback thread destory.
2025-08-27 13:48:34.521 INFO  [main] org.springframework.boot.StartupInfoLogger.logStarting:55 - [traceId:] Starting AntFlowApplication using Java 1.8.0_432 on KyleHsudeMacBook-Pro.local with PID 42748 (/Users/<USER>/shuma/source-code/Antflow/antflow-web/target/classes started by kylehsu in /Users/<USER>/shuma/source-code/Antflow)
2025-08-27 13:48:34.522 INFO  [main] org.springframework.boot.SpringApplication.logStartupProfileInfo:638 - [traceId:] The following 1 profile is active: "pre"
2025-08-27 13:48:35.085 INFO  [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate.multipleStoresDetected:262 - [traceId:] Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-27 13:48:35.086 INFO  [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate.registerRepositoriesIn:132 - [traceId:] Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-27 13:48:35.176 INFO  [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate.registerRepositoriesIn:201 - [traceId:] Finished Spring Data repository scanning in 60 ms. Found 0 Redis repository interfaces.
2025-08-27 13:48:35.702 INFO  [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize:108 - [traceId:] Tomcat initialized with port(s): 7001 (http)
2025-08-27 13:48:35.706 INFO  [main] org.apache.juli.logging.DirectJDKLog.log:173 - [traceId:] Starting service [Tomcat]
2025-08-27 13:48:35.706 INFO  [main] org.apache.juli.logging.DirectJDKLog.log:173 - [traceId:] Starting Servlet engine: [Apache Tomcat/9.0.82]
2025-08-27 13:48:35.746 INFO  [main] org.apache.juli.logging.DirectJDKLog.log:173 - [traceId:] Initializing Spring embedded WebApplicationContext
2025-08-27 13:48:35.746 INFO  [main] org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.prepareWebApplicationContext:292 - [traceId:] Root WebApplicationContext: initialization completed in 1123 ms
2025-08-27 13:48:36.402 INFO  [main] com.zaxxer.hikari.HikariDataSource.getConnection:110 - [traceId:] HikariPool-1 - Starting...
2025-08-27 13:48:37.150 INFO  [main] com.zaxxer.hikari.HikariDataSource.getConnection:123 - [traceId:] HikariPool-1 - Start completed.
2025-08-27 13:48:37.402 INFO  [main] org.activiti.engine.impl.ProcessEngineImpl.<init>:86 - [traceId:] ProcessEngine default created
2025-08-27 13:48:37.404 INFO  [main] org.openoa.engine.bpmnconf.activitilistener.BpmnGlobalEventListener.onEvent:45 - [traceId:] AntFlow Engine is started
2025-08-27 13:48:37.404 INFO  [main] org.activiti.engine.impl.cfg.multitenant.MultiSchemaMultiTenantProcessEngineConfiguration.createTenantSchema:160 - [traceId:] creating/validating database schema for tenant 
2025-08-27 13:48:39.637 INFO  [main] org.openoa.base.dto.wecom.WechatConfigProperties.init:40 - [traceId:] ===> wechat config: WechatConfigProperties(zhongqi=WechatConfigProperties.WechatConfig(agentId=1000229, corpId=wwf1c477c862181a7f, corpsecret=IH5PGBOHD1dOMX86TOHi8UclM7uUUzaODKvbmE2m-ZA, approveDomain=test-approve.ceboss.cn), chenxing=WechatConfigProperties.WechatConfig(agentId=1000099, corpId=wwe2351ed80f0479d4, corpsecret=_PhZTbZXs6-zucZq1efQXWW5L3ya69RUkTgQRj0eWQk, approveDomain=test-approve.oristarcloud.com), xinwang=WechatConfigProperties.WechatConfig(agentId=1000129, corpId=wwd674e2ffdc4f6862, corpsecret=kG1XfR6fwFoPBRBIPev92WVw5QtOu2UB0AiNx0TluG4, approveDomain=test-approve.xinnet.com), kuajing=WechatConfigProperties.WechatConfig(agentId=1000060, corpId=ww8bd429f5b618a57e, corpsecret=SFO9FKx4G0Yl_y5E8Jw5cf0DJHmuVisRsOQouayXYkM, approveDomain=test-approve.gboss.tech), jituan=WechatConfigProperties.WechatConfig(agentId=1000008, corpId=wwa0a360c6341c5ae6, corpsecret=GnyB1Y1-SWkutF8_vIDeyGj3WsYqzuDTo9eB5kzJbOg, approveDomain=test-approve.ce-group.cn), smhg=WechatConfigProperties.WechatConfig(agentId=999999, corpId=999999, corpsecret=999999, approveDomain=999999), xiaoxia=WechatConfigProperties.WechatConfig(agentId=1000011, corpId=ww71746e109858902c, corpsecret=jAYDpVHoRihTuXLJ8Zm1HPV5M1t9XMi7-861mGdl4rA, approveDomain=test-approve.xiaoxiatech.com))
2025-08-27 13:48:40.562 INFO  [main] cn.ce.xxljob.config.XxlAdminAddressConfig.afterPropertiesSet:91 - [traceId:] 处理配置项: xxl.job.admin.addresses:http://pre-omo.aiyouyi.cn/xxl-job-admin/
2025-08-27 13:48:40.562 INFO  [main] cn.ce.xxljob.config.XxlAdminAddressConfig.afterPropertiesSet:109 - [traceId:] Xxl Job Master Admin URL: http://pre-omo.aiyouyi.cn/xxl-job-admin/
2025-08-27 13:48:40.564 INFO  [main] cn.ce.xxljob.config.XxlJobConfig.xxlJobExecutor:46 - [traceId:] >>>>>>>>>>> xxl-job config init.
2025-08-27 13:48:40.867 INFO  [main] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:168 - [traceId:] >>>>>>>>>>> xxl-job register jobhandler success, name:ANTFLOW_SYNC_EHR_EMP_INFO_JOB_HANDLER, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@51b41740[class org.openoa.job.SyncEhrDataJob#syncEhrEmpInfoJobHandler]
2025-08-27 13:48:40.867 INFO  [main] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:168 - [traceId:] >>>>>>>>>>> xxl-job register jobhandler success, name:ANTFLOW_SYNC_EHR_ORG_INFO_JOB_HANDLER, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@70cac22a[class org.openoa.job.SyncEhrDataJob#syncEhrOrgInfoJobHandler]
2025-08-27 13:48:40.966 INFO  [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer.start:220 - [traceId:] Tomcat started on port(s): 7001 (http) with context path ''
2025-08-27 13:48:40.986 INFO  [Thread-18] com.xxl.job.core.server.EmbedServer$1.run:86 - [traceId:] >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9421
2025-08-27 13:48:42.032 INFO  [main] org.springframework.boot.StartupInfoLogger.logStarted:61 - [traceId:] Started AntFlowApplication in 7.936 seconds (JVM running for 8.303)
2025-08-27 13:48:42.034 INFO  [main] org.openoa.common.config.StartedListener.onApplicationEvent:25 - [traceId:] [94myour project server started at: http://127.0.0.1:7001[0;39m
2025-08-27 13:48:42.034 INFO  [main] org.openoa.common.config.StartedListener.onApplicationEvent:27 - [traceId:] [93myour project server was started successfully on port:7001 with context path:/[0;39m
2025-08-27 13:48:42.034 ERROR [main] org.openoa.AntFlowApplication.main:15 - [traceId:] AntFlow 项目启动成功!
2025-08-27 13:48:42.057 INFO  [pool-4-thread-1] com.nrcp.log.ErrorLogSender$1.run:100 - [traceId:] ErrorLogSender send error log [pre 环境antflow项目 TraceId: null ip: ************** Exception: AntFlow 项目启动成功! argsList: null]
2025-08-27 13:48:42.416 INFO  [pool-4-thread-1] com.nrcp.log.CompanyWeChatAlarmService.sendAlarm:50 - [traceId:] alarm call wechat roobat response info [{"errcode":0,"errmsg":"ok"}]
2025-08-27 13:48:50.027 INFO  [http-nio-7001-exec-1] org.apache.juli.logging.DirectJDKLog.log:173 - [traceId:] Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-27 13:48:50.032 INFO  [http-nio-7001-exec-1] org.springframework.web.servlet.FrameworkServlet.initServletBean:525 - [traceId:] Initializing Servlet 'dispatcherServlet'
2025-08-27 13:48:50.033 INFO  [http-nio-7001-exec-1] org.springframework.web.servlet.FrameworkServlet.initServletBean:547 - [traceId:] Completed initialization in 1 ms
2025-08-27 13:48:50.282 INFO  [http-nio-7001-exec-1] org.openoa.aspect.AuthInterceptor.preHandle:25 - [traceId:00000000.1756273730055.*********] 员工=侯晓瑶, jt83326, 请求路径：/bpmnConf/detail/658
2025-08-27 13:48:50.904 INFO  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.detail:507 - [traceId:00000000.1756273730055.*********] 查询数据库获取模板详情，模板id=658
2025-08-27 13:48:52.085 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【终试结果抄送】区城人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:48:52.299 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件1 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:48:52.596 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【终试结果抄送】区城人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:48:52.872 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件2 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:48:53.018 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【终试结果抄送】区城人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:48:53.240 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件3 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:48:53.380 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【终试结果抄送】区城人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:48:53.604 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件4 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:48:53.744 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【终试结果抄送】区城人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:48:53.963 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件5 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:48:54.118 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【终试结果抄送】区城人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:48:54.329 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件6 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:48:54.476 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【终试结果抄送】区城人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:48:54.752 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件7 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:48:54.890 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【终试结果抄送】区城人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:48:55.110 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件8 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:48:55.267 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件9 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:48:55.322 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【终试结果抄送】：分公司 HRBP、商务经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:48:55.698 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 分公司总监终试结果确认 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyCustomizeAdp@514fadc5
2025-08-27 13:48:55.769 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【试岗结果抄送】区域人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:48:55.987 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件1 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:48:56.119 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【试岗结果抄送】区域人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:48:56.337 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件2 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:48:56.483 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【试岗结果抄送】区域人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:48:56.708 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件3 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:48:56.877 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【试岗结果抄送】区域人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:48:57.113 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件4 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:48:57.254 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【试岗结果抄送】区域人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:48:57.527 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件5 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:48:57.671 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【试岗结果抄送】区域人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:48:57.889 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件6 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:48:58.033 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【试岗结果抄送】区域人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:48:58.288 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件7 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:48:58.436 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【试岗结果抄送】区域人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:48:58.644 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件8 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:48:59.036 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件9 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:48:59.152 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【试岗结果抄送】分司HRBP、商务经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:49:00.099 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 商务经理确认试岗结果 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyCustomizeAdp@514fadc5
2025-08-27 13:49:00.273 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【区域试岗评价抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:49:00.758 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件1 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:49:00.868 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【区域试岗评价抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:49:01.070 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件2 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:49:01.175 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【区域试岗评价抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:49:01.380 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件3 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:49:01.530 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【区域试岗评价抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:49:01.737 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件4 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:49:01.889 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【区域试岗评价抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:49:02.110 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件5 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:49:02.225 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【区域试岗评价抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:49:02.426 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件6 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:49:02.545 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【区域试岗评价抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:49:02.764 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件7 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:49:02.865 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【区域试岗评价抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:49:03.085 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件8 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:49:03.188 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件9 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:49:03.247 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【区域试岗评价抄送】：分公司HRBP、商务经理、分公司总监 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:49:03.538 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 区域培训经理考核试岗 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyCustomizeAdp@514fadc5
2025-08-27 13:49:03.618 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【确认试岗抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:49:03.829 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件1 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:49:03.934 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【确认试岗抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:49:04.138 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件2 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:49:04.242 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【确认试岗抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:49:04.446 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件3 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:49:04.546 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【确认试岗抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:49:04.755 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件4 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:49:04.915 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【确认试岗抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:49:05.123 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件5 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:49:05.272 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【确认试岗抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:49:05.478 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件6 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:49:05.581 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【确认试岗抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:49:05.824 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件7 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:49:05.915 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【确认试岗抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:49:06.125 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件8 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:49:06.229 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件9 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:49:06.286 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【确认试岗抄送】商务经理、分公司总监 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:49:06.542 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 分司HRBP确认已来试岗 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyCustomizeAdp@514fadc5
2025-08-27 13:49:06.602 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【试岗时间沟通结果抄送】区域人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:49:06.830 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件1 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:49:06.953 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【试岗时间沟通结果抄送】区域人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:49:07.158 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件2 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:49:07.279 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【试岗时间沟通结果抄送】区域人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:49:07.477 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件3 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:49:07.625 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【试岗时间沟通结果抄送】区域人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:49:07.824 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件4 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:49:07.976 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【试岗时间沟通结果抄送】区域人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:49:08.218 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件5 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:49:08.351 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【试岗时间沟通结果抄送】区域人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:49:08.562 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件6 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:49:08.722 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【试岗时间沟通结果抄送】区域人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:49:08.991 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件7 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:49:09.111 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【试岗时间沟通结果抄送】区域人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:49:09.334 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件8 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:49:09.476 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件9 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:49:09.534 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【试岗时间沟通结果抄送】分公司HRBP、商务经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:49:09.794 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 区域招聘专员沟通试岗时间 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyStartUserAdp@5f75bd1
2025-08-27 13:49:09.795 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【现场面试结果反馈抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:49:10.003 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件1 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:49:10.112 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【现场面试结果反馈抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:49:10.312 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件2 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:49:10.428 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【现场面试结果反馈抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:49:10.627 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件3 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:49:10.730 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【现场面试结果反馈抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:49:10.958 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件4 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:49:11.101 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【现场面试结果反馈抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:49:11.302 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件5 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:49:11.404 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【现场面试结果反馈抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:49:11.619 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件6 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:49:11.724 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【现场面试结果反馈抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:49:11.866 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件8 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:49:12.008 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【现场面试结果反馈抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:49:12.211 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件7 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:49:12.316 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件9 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:49:12.369 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 商务经理现场面试 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyCustomizeAdp@514fadc5
2025-08-27 13:49:12.439 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【面试时间再次确认环节抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:49:12.670 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件1 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:49:12.815 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【面试时间再次确认环节抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:49:13.026 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件2 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:49:13.164 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【面试时间再次确认环节抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:49:13.301 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件3 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:49:13.403 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【面试时间再次确认环节抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:49:13.644 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件4 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:49:13.786 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【面试时间再次确认环节抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:49:14.000 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件5 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:49:14.157 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【面试时间再次确认环节抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:49:14.393 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件6 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:49:14.497 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【面试时间再次确认环节抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:49:14.700 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件7 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:49:14.833 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【面试时间再次确认环节抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:49:15.041 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件8 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:49:15.151 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件9 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:49:15.205 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【面试时间再次确认环节抄送】分公司 HRBP、商务经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:49:15.494 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 分公司HRBP面试时间再次确认 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyCustomizeAdp@514fadc5
2025-08-27 13:49:15.558 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【面试安排环节抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:49:15.791 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件1 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:49:15.943 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【面试安排环节抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:49:16.149 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件2 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:49:16.262 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【面试安排环节抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:49:16.466 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件3 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:49:16.570 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【面试安排环节抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:49:16.781 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件4 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:49:16.928 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【面试安排环节抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:49:17.135 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件5 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:49:17.242 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【面试安排环节抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:49:17.460 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件6 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:49:17.560 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【面试安排环节抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:49:17.793 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件7 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:49:17.935 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【面试安排环节抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:49:18.169 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件8 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:49:18.288 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 条件9 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:49:18.341 WARN  [http-nio-7001-exec-1] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273730055.*********] ----> 【面试安排环节抄送】分公司HRBP、商务经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:30.002 INFO  [http-nio-7001-exec-2] org.openoa.aspect.AuthInterceptor.preHandle:25 - [traceId:00000000.1756273829809.427481001] 员工=侯晓瑶, jt83326, 请求路径：/bpmnConf/detail/658
2025-08-27 13:50:30.100 INFO  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.detail:507 - [traceId:00000000.1756273829809.427481001] 查询数据库获取模板详情，模板id=658
2025-08-27 13:50:31.338 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【终试结果抄送】区城人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:31.558 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件1 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:31.672 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【终试结果抄送】区城人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:31.897 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件2 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:32.060 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【终试结果抄送】区城人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:32.281 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件3 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:32.441 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【终试结果抄送】区城人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:32.669 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件4 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:32.850 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【终试结果抄送】区城人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:33.109 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件5 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:33.239 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【终试结果抄送】区城人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:33.515 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件6 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:33.627 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【终试结果抄送】区城人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:33.909 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件7 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:34.026 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【终试结果抄送】区城人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:34.249 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件8 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:34.373 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件9 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:34.429 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【终试结果抄送】：分公司 HRBP、商务经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:34.868 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 分公司总监终试结果确认 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyCustomizeAdp@514fadc5
2025-08-27 13:50:34.965 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【试岗结果抄送】区域人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:35.204 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件1 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:35.423 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【试岗结果抄送】区域人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:35.676 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件2 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:35.800 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【试岗结果抄送】区域人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:36.055 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件3 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:36.198 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【试岗结果抄送】区域人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:36.431 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件4 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:36.570 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【试岗结果抄送】区域人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:36.812 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件5 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:36.943 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【试岗结果抄送】区域人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:37.161 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件6 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:37.280 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【试岗结果抄送】区域人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:37.584 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件7 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:37.763 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【试岗结果抄送】区域人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:38.030 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件8 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:38.148 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件9 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:38.212 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【试岗结果抄送】分司HRBP、商务经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:38.516 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 商务经理确认试岗结果 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyCustomizeAdp@514fadc5
2025-08-27 13:50:38.592 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【区域试岗评价抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:38.829 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件1 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:38.957 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【区域试岗评价抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:39.202 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件2 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:39.351 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【区域试岗评价抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:39.627 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件3 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:39.758 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【区域试岗评价抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:39.997 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件4 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:40.169 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【区域试岗评价抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:40.412 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件5 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:40.558 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【区域试岗评价抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:40.844 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件6 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:41.009 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【区域试岗评价抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:41.274 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件7 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:41.417 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【区域试岗评价抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:41.643 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件8 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:41.798 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件9 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:41.862 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【区域试岗评价抄送】：分公司HRBP、商务经理、分公司总监 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:42.213 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 区域培训经理考核试岗 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyCustomizeAdp@514fadc5
2025-08-27 13:50:42.250 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【确认试岗抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:42.519 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件1 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:42.691 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【确认试岗抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:42.961 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件2 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:43.105 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【确认试岗抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:43.345 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件3 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:43.475 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【确认试岗抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:43.716 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件4 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:43.854 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【确认试岗抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:44.096 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件5 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:44.232 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【确认试岗抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:44.497 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件6 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:44.617 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【确认试岗抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:44.873 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件7 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:45.005 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【确认试岗抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:45.214 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件8 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:45.333 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件9 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:45.391 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【确认试岗抄送】商务经理、分公司总监 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:45.660 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 分司HRBP确认已来试岗 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyCustomizeAdp@514fadc5
2025-08-27 13:50:45.728 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【试岗时间沟通结果抄送】区域人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:45.958 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件1 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:46.068 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【试岗时间沟通结果抄送】区域人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:46.294 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件2 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:46.411 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【试岗时间沟通结果抄送】区域人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:46.638 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件3 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:46.799 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【试岗时间沟通结果抄送】区域人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:47.020 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件4 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:47.126 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【试岗时间沟通结果抄送】区域人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:47.342 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件5 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:47.455 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【试岗时间沟通结果抄送】区域人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:47.684 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件6 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:47.832 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【试岗时间沟通结果抄送】区域人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:48.092 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件7 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:48.205 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【试岗时间沟通结果抄送】区域人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:48.434 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件8 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:48.592 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件9 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:48.653 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【试岗时间沟通结果抄送】分公司HRBP、商务经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:48.960 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 区域招聘专员沟通试岗时间 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyStartUserAdp@5f75bd1
2025-08-27 13:50:48.960 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【现场面试结果反馈抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:49.161 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件1 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:49.281 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【现场面试结果反馈抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:49.498 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件2 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:49.634 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【现场面试结果反馈抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:49.855 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件3 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:49.975 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【现场面试结果反馈抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:50.210 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件4 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:50.368 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【现场面试结果反馈抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:50.599 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件5 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:50.710 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【现场面试结果反馈抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:50.934 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件6 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:51.052 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【现场面试结果反馈抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:51.291 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件8 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:51.434 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【现场面试结果反馈抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:51.667 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件7 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:51.786 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件9 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:51.842 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 商务经理现场面试 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyCustomizeAdp@514fadc5
2025-08-27 13:50:51.916 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【面试时间再次确认环节抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:52.155 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件1 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:52.272 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【面试时间再次确认环节抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:52.490 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件2 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:52.619 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【面试时间再次确认环节抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:52.854 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件3 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:52.978 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【面试时间再次确认环节抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:53.208 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件4 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:53.370 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【面试时间再次确认环节抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:53.587 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件5 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:53.724 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【面试时间再次确认环节抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:53.940 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件6 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:54.052 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【面试时间再次确认环节抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:54.310 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件7 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:54.449 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【面试时间再次确认环节抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:54.671 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件8 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:54.790 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件9 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:54.851 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【面试时间再次确认环节抄送】分公司 HRBP、商务经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:55.136 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 分公司HRBP面试时间再次确认 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyCustomizeAdp@514fadc5
2025-08-27 13:50:55.204 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【面试安排环节抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:55.432 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件1 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:55.535 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【面试安排环节抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:55.750 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件2 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:55.868 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【面试安排环节抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:56.092 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件3 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:56.209 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【面试安排环节抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:56.428 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件4 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:56.551 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【面试安排环节抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:56.771 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件5 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:56.909 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【面试安排环节抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:57.113 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件6 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:57.229 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【面试安排环节抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:57.465 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件7 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:57.620 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【面试安排环节抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:50:57.829 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件8 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:57.991 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 条件9 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:50:58.052 WARN  [http-nio-7001-exec-2] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273829809.427481001] ----> 【面试安排环节抄送】分公司HRBP、商务经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:51:37.836 INFO  [http-nio-7001-exec-3] org.openoa.aspect.AuthInterceptor.preHandle:25 - [traceId:00000000.1756273897629.427481002] 员工=侯晓瑶, jt83326, 请求路径：/bpmnConf/detail/658
2025-08-27 13:51:37.928 INFO  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.detail:507 - [traceId:00000000.1756273897629.427481002] 查询数据库获取模板详情，模板id=658
2025-08-27 13:51:39.291 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【终试结果抄送】区城人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:51:39.521 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件1 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:51:39.637 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【终试结果抄送】区城人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:51:39.874 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件2 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:51:39.992 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【终试结果抄送】区城人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:51:40.236 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件3 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:51:40.400 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【终试结果抄送】区城人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:51:40.618 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件4 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:51:40.741 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【终试结果抄送】区城人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:51:40.950 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件5 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:51:41.118 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【终试结果抄送】区城人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:51:41.368 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件6 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:51:41.539 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【终试结果抄送】区城人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:51:41.817 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件7 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:51:41.993 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【终试结果抄送】区城人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:51:42.211 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件8 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:51:42.295 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件9 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:51:42.334 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【终试结果抄送】：分公司 HRBP、商务经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:51:42.575 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 分公司总监终试结果确认 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyCustomizeAdp@514fadc5
2025-08-27 13:51:42.640 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【试岗结果抄送】区域人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:51:42.871 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件1 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:51:42.977 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【试岗结果抄送】区域人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:51:43.196 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件2 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:51:43.309 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【试岗结果抄送】区域人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:51:43.540 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件3 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:51:43.700 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【试岗结果抄送】区域人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:51:43.911 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件4 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:51:44.028 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【试岗结果抄送】区域人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:51:44.263 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件5 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:51:44.387 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【试岗结果抄送】区域人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:51:44.617 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件6 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:51:44.724 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【试岗结果抄送】区域人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:51:45.009 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件7 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:51:45.115 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【试岗结果抄送】区域人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:51:45.332 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件8 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:51:45.458 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件9 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:51:45.518 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【试岗结果抄送】分司HRBP、商务经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:51:45.865 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 商务经理确认试岗结果 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyCustomizeAdp@514fadc5
2025-08-27 13:51:45.936 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【区域试岗评价抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:51:46.167 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件1 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:51:46.279 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【区域试岗评价抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:51:46.499 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件2 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:51:46.620 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【区域试岗评价抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:51:46.855 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件3 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:51:46.976 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【区域试岗评价抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:51:47.204 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件4 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:51:47.308 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【区域试岗评价抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:51:47.525 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件5 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:51:47.645 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【区域试岗评价抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:51:47.864 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件6 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:51:47.987 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【区域试岗评价抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:51:48.299 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件7 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:51:48.458 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【区域试岗评价抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:51:48.678 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件8 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:51:48.807 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件9 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:51:48.869 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【区域试岗评价抄送】：分公司HRBP、商务经理、分公司总监 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:51:49.173 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 区域培训经理考核试岗 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyCustomizeAdp@514fadc5
2025-08-27 13:51:49.201 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【确认试岗抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:51:49.450 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件1 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:51:49.576 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【确认试岗抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:51:49.812 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件2 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:51:49.935 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【确认试岗抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:51:50.154 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件3 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:51:50.279 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【确认试岗抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:51:50.499 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件4 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:51:50.615 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【确认试岗抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:51:50.847 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件5 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:51:50.980 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【确认试岗抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:51:51.194 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件6 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:51:51.317 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【确认试岗抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:51:51.572 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件7 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:51:51.743 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【确认试岗抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:51:51.973 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件8 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:51:52.095 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件9 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:51:52.156 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【确认试岗抄送】商务经理、分公司总监 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:51:52.399 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 分司HRBP确认已来试岗 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyCustomizeAdp@514fadc5
2025-08-27 13:51:52.463 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【试岗时间沟通结果抄送】区域人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:51:52.693 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件1 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:51:52.817 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【试岗时间沟通结果抄送】区域人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:51:53.044 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件2 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:51:53.175 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【试岗时间沟通结果抄送】区域人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:51:53.396 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件3 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:51:53.524 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【试岗时间沟通结果抄送】区域人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:51:53.736 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件4 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:51:53.857 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【试岗时间沟通结果抄送】区域人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:51:54.081 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件5 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:51:54.249 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【试岗时间沟通结果抄送】区域人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:51:54.480 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件6 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:51:54.644 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【试岗时间沟通结果抄送】区域人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:51:54.923 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件7 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:51:55.080 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【试岗时间沟通结果抄送】区域人力行政经理、区域培训经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:51:55.301 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件8 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:51:55.462 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件9 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:51:55.521 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【试岗时间沟通结果抄送】分公司HRBP、商务经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:51:55.816 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 区域招聘专员沟通试岗时间 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyStartUserAdp@5f75bd1
2025-08-27 13:51:55.817 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【现场面试结果反馈抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:51:56.017 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件1 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:51:56.145 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【现场面试结果反馈抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:51:56.360 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件2 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:51:56.473 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【现场面试结果反馈抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:51:56.696 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件3 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:51:56.810 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【现场面试结果反馈抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:51:57.034 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件4 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:51:57.167 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【现场面试结果反馈抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:51:57.364 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件5 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:51:57.477 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【现场面试结果反馈抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:51:57.770 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件6 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:51:57.920 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【现场面试结果反馈抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:51:58.170 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件8 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:51:58.325 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【现场面试结果反馈抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:51:58.545 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件7 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:51:58.675 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件9 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:51:58.751 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 商务经理现场面试 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyCustomizeAdp@514fadc5
2025-08-27 13:51:58.825 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【面试时间再次确认环节抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:51:59.062 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件1 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:51:59.183 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【面试时间再次确认环节抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:51:59.418 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件2 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:51:59.543 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【面试时间再次确认环节抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:51:59.754 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件3 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:51:59.867 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【面试时间再次确认环节抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:52:00.085 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件4 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:52:00.194 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【面试时间再次确认环节抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:52:00.482 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件5 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:52:00.627 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【面试时间再次确认环节抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:52:00.833 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件6 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:52:00.938 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【面试时间再次确认环节抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:52:01.175 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件7 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:52:01.321 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【面试时间再次确认环节抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:52:01.528 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件8 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:52:01.645 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件9 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:52:01.701 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【面试时间再次确认环节抄送】分公司 HRBP、商务经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:52:02.106 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 分公司HRBP面试时间再次确认 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyCustomizeAdp@514fadc5
2025-08-27 13:52:02.178 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【面试安排环节抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:52:02.432 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件1 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:52:02.555 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【面试安排环节抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:52:02.775 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件2 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:52:02.907 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【面试安排环节抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:52:03.148 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件3 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:52:03.292 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【面试安排环节抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:52:03.506 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件4 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:52:03.625 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【面试安排环节抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:52:03.844 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件5 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:52:03.951 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【面试安排环节抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:52:04.177 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件6 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:52:04.293 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【面试安排环节抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:52:04.533 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件7 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:52:04.684 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【面试安排环节抄送】区域人力行政经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:52:04.912 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件8 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:52:05.044 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 条件9 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodeTypeConditionsAdp@5c1cc6a3
2025-08-27 13:52:05.102 WARN  [http-nio-7001-exec-3] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.getBpmnNodeVo:1052 - [traceId:00000000.1756273897629.427481002] ----> 【面试安排环节抄送】分公司HRBP、商务经理 --- bpmnNodeAdaptor= org.openoa.engine.bpmnconf.adp.bpmnnodeadp.NodePropertyPersonnelAdp@79325c5f
2025-08-27 13:52:41.582 INFO  [http-nio-7001-exec-4] org.openoa.aspect.AuthInterceptor.preHandle:25 - [traceId:00000000.1756273961393.427481003] 员工=侯晓瑶, jt83326, 请求路径：/bpmnConf/detail/658
2025-08-27 13:52:42.467 INFO  [http-nio-7001-exec-4] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.detail:504 - [traceId:00000000.1756273961393.427481003] 查询缓存获取模板详情，模板id=658
2025-08-27 13:52:48.888 INFO  [http-nio-7001-exec-5] org.openoa.aspect.AuthInterceptor.preHandle:25 - [traceId:00000000.1756273968710.427481004] 员工=侯晓瑶, jt83326, 请求路径：/bpmnConf/detail/658
2025-08-27 13:52:50.896 INFO  [http-nio-7001-exec-5] org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl.detail:504 - [traceId:00000000.1756273968710.427481004] 查询缓存获取模板详情，模板id=658
2025-08-27 13:53:20.104 INFO  [Thread-18] com.xxl.job.core.server.EmbedServer$1.run:96 - [traceId:] >>>>>>>>>>> xxl-job remoting server stop.
2025-08-27 13:53:20.201 INFO  [xxl-job, executor ExecutorRegistryThread] com.xxl.job.core.thread.ExecutorRegistryThread$1.run:87 - [traceId:] >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='antflow-pre', registryValue='http://**************:9421/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-08-27 13:53:20.201 INFO  [xxl-job, executor ExecutorRegistryThread] com.xxl.job.core.thread.ExecutorRegistryThread$1.run:105 - [traceId:] >>>>>>>>>>> xxl-job, executor registry thread destory.
2025-08-27 13:53:20.201 INFO  [SpringApplicationShutdownHook] com.xxl.job.core.server.EmbedServer.stop:125 - [traceId:] >>>>>>>>>>> xxl-job remoting server destroy success.
2025-08-27 13:53:20.202 INFO  [xxl-job, executor JobLogFileCleanThread] com.xxl.job.core.thread.JobLogFileCleanThread$1.run:99 - [traceId:] >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2025-08-27 13:53:20.202 INFO  [xxl-job, executor TriggerCallbackThread] com.xxl.job.core.thread.TriggerCallbackThread$1.run:97 - [traceId:] >>>>>>>>>>> xxl-job, executor callback thread destory.
2025-08-27 13:53:20.202 INFO  [Thread-17] com.xxl.job.core.thread.TriggerCallbackThread$2.run:127 - [traceId:] >>>>>>>>>>> xxl-job, executor retry callback thread destory.
2025-08-27 13:58:53.029 INFO  [main] org.springframework.boot.StartupInfoLogger.logStarting:55 - [traceId:] Starting AntFlowApplication using Java 1.8.0_432 on KyleHsudeMacBook-Pro.local with PID 44370 (/Users/<USER>/shuma/source-code/Antflow/antflow-web/target/classes started by kylehsu in /Users/<USER>/shuma/source-code/Antflow)
2025-08-27 13:58:53.030 INFO  [main] org.springframework.boot.SpringApplication.logStartupProfileInfo:638 - [traceId:] The following 1 profile is active: "pre"
2025-08-27 13:58:53.648 INFO  [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate.multipleStoresDetected:262 - [traceId:] Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-27 13:58:53.649 INFO  [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate.registerRepositoriesIn:132 - [traceId:] Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-27 13:58:53.717 INFO  [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate.registerRepositoriesIn:201 - [traceId:] Finished Spring Data repository scanning in 61 ms. Found 0 Redis repository interfaces.
2025-08-27 13:58:54.351 INFO  [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize:108 - [traceId:] Tomcat initialized with port(s): 7001 (http)
2025-08-27 13:58:54.355 INFO  [main] org.apache.juli.logging.DirectJDKLog.log:173 - [traceId:] Starting service [Tomcat]
2025-08-27 13:58:54.355 INFO  [main] org.apache.juli.logging.DirectJDKLog.log:173 - [traceId:] Starting Servlet engine: [Apache Tomcat/9.0.82]
2025-08-27 13:58:54.397 INFO  [main] org.apache.juli.logging.DirectJDKLog.log:173 - [traceId:] Initializing Spring embedded WebApplicationContext
2025-08-27 13:58:54.397 INFO  [main] org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.prepareWebApplicationContext:292 - [traceId:] Root WebApplicationContext: initialization completed in 1244 ms
2025-08-27 13:58:55.078 INFO  [main] com.zaxxer.hikari.HikariDataSource.getConnection:110 - [traceId:] HikariPool-1 - Starting...
2025-08-27 13:58:55.851 INFO  [main] com.zaxxer.hikari.HikariDataSource.getConnection:123 - [traceId:] HikariPool-1 - Start completed.
2025-08-27 13:58:56.088 INFO  [main] org.activiti.engine.impl.ProcessEngineImpl.<init>:86 - [traceId:] ProcessEngine default created
2025-08-27 13:58:56.089 INFO  [main] org.openoa.engine.bpmnconf.activitilistener.BpmnGlobalEventListener.onEvent:45 - [traceId:] AntFlow Engine is started
2025-08-27 13:58:56.089 INFO  [main] org.activiti.engine.impl.cfg.multitenant.MultiSchemaMultiTenantProcessEngineConfiguration.createTenantSchema:160 - [traceId:] creating/validating database schema for tenant 
2025-08-27 13:58:59.371 INFO  [main] org.openoa.base.dto.wecom.WechatConfigProperties.init:40 - [traceId:] ===> wechat config: WechatConfigProperties(zhongqi=WechatConfigProperties.WechatConfig(agentId=1000229, corpId=wwf1c477c862181a7f, corpsecret=IH5PGBOHD1dOMX86TOHi8UclM7uUUzaODKvbmE2m-ZA, approveDomain=test-approve.ceboss.cn), chenxing=WechatConfigProperties.WechatConfig(agentId=1000099, corpId=wwe2351ed80f0479d4, corpsecret=_PhZTbZXs6-zucZq1efQXWW5L3ya69RUkTgQRj0eWQk, approveDomain=test-approve.oristarcloud.com), xinwang=WechatConfigProperties.WechatConfig(agentId=1000129, corpId=wwd674e2ffdc4f6862, corpsecret=kG1XfR6fwFoPBRBIPev92WVw5QtOu2UB0AiNx0TluG4, approveDomain=test-approve.xinnet.com), kuajing=WechatConfigProperties.WechatConfig(agentId=1000060, corpId=ww8bd429f5b618a57e, corpsecret=SFO9FKx4G0Yl_y5E8Jw5cf0DJHmuVisRsOQouayXYkM, approveDomain=test-approve.gboss.tech), jituan=WechatConfigProperties.WechatConfig(agentId=1000008, corpId=wwa0a360c6341c5ae6, corpsecret=GnyB1Y1-SWkutF8_vIDeyGj3WsYqzuDTo9eB5kzJbOg, approveDomain=test-approve.ce-group.cn), smhg=WechatConfigProperties.WechatConfig(agentId=999999, corpId=999999, corpsecret=999999, approveDomain=999999), xiaoxia=WechatConfigProperties.WechatConfig(agentId=1000011, corpId=ww71746e109858902c, corpsecret=jAYDpVHoRihTuXLJ8Zm1HPV5M1t9XMi7-861mGdl4rA, approveDomain=test-approve.xiaoxiatech.com))
2025-08-27 13:59:00.430 INFO  [main] cn.ce.xxljob.config.XxlAdminAddressConfig.afterPropertiesSet:91 - [traceId:] 处理配置项: xxl.job.admin.addresses:http://pre-omo.aiyouyi.cn/xxl-job-admin/
2025-08-27 13:59:00.431 INFO  [main] cn.ce.xxljob.config.XxlAdminAddressConfig.afterPropertiesSet:109 - [traceId:] Xxl Job Master Admin URL: http://pre-omo.aiyouyi.cn/xxl-job-admin/
2025-08-27 13:59:00.433 INFO  [main] cn.ce.xxljob.config.XxlJobConfig.xxlJobExecutor:46 - [traceId:] >>>>>>>>>>> xxl-job config init.
2025-08-27 13:59:00.733 INFO  [main] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:168 - [traceId:] >>>>>>>>>>> xxl-job register jobhandler success, name:ANTFLOW_SYNC_EHR_EMP_INFO_JOB_HANDLER, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@10508ba6[class org.openoa.job.SyncEhrDataJob#syncEhrEmpInfoJobHandler]
2025-08-27 13:59:00.734 INFO  [main] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:168 - [traceId:] >>>>>>>>>>> xxl-job register jobhandler success, name:ANTFLOW_SYNC_EHR_ORG_INFO_JOB_HANDLER, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@da11873[class org.openoa.job.SyncEhrDataJob#syncEhrOrgInfoJobHandler]
2025-08-27 13:59:00.821 INFO  [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer.start:220 - [traceId:] Tomcat started on port(s): 7001 (http) with context path ''
2025-08-27 13:59:00.841 INFO  [Thread-21] com.xxl.job.core.server.EmbedServer$1.run:86 - [traceId:] >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9421
2025-08-27 13:59:01.876 INFO  [main] org.springframework.boot.StartupInfoLogger.logStarted:61 - [traceId:] Started AntFlowApplication in 9.313 seconds (JVM running for 9.673)
2025-08-27 13:59:01.878 INFO  [main] org.openoa.common.config.StartedListener.onApplicationEvent:25 - [traceId:] [94myour project server started at: http://127.0.0.1:7001[0;39m
2025-08-27 13:59:01.878 INFO  [main] org.openoa.common.config.StartedListener.onApplicationEvent:27 - [traceId:] [93myour project server was started successfully on port:7001 with context path:/[0;39m
2025-08-27 13:59:01.878 ERROR [main] org.openoa.AntFlowApplication.main:15 - [traceId:] AntFlow 项目启动成功!
2025-08-27 13:59:01.901 INFO  [pool-4-thread-1] com.nrcp.log.ErrorLogSender$1.run:100 - [traceId:] ErrorLogSender send error log [pre 环境antflow项目 TraceId: null ip: ************** Exception: AntFlow 项目启动成功! argsList: null]
2025-08-27 13:59:02.265 INFO  [pool-4-thread-1] com.nrcp.log.CompanyWeChatAlarmService.sendAlarm:50 - [traceId:] alarm call wechat roobat response info [{"errcode":0,"errmsg":"ok"}]
2025-08-27 14:05:36.058 INFO  [http-nio-7001-exec-1] org.apache.juli.logging.DirectJDKLog.log:173 - [traceId:] Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-27 14:05:36.059 INFO  [http-nio-7001-exec-1] org.springframework.web.servlet.FrameworkServlet.initServletBean:525 - [traceId:] Initializing Servlet 'dispatcherServlet'
2025-08-27 14:05:36.060 INFO  [http-nio-7001-exec-1] org.springframework.web.servlet.FrameworkServlet.initServletBean:547 - [traceId:] Completed initialization in 1 ms
2025-08-27 14:05:36.302 INFO  [http-nio-7001-exec-1] org.openoa.aspect.AuthInterceptor.preHandle:25 - [traceId:00000000.1756274736076.*********] 员工=侯晓瑶, jt83326, 请求路径：/user/queryByNameFuzzy
2025-08-27 14:05:37.152 ERROR [http-nio-7001-exec-1] org.openoa.common.config.mvc.GlobalExceptionHandler.handler:43 - [traceId:00000000.1756274736076.*********] 全局异常捕获: Handler dispatch failed; nested exception is java.lang.ExceptionInInitializerError
org.springframework.web.util.NestedServletException: Handler dispatch failed; nested exception is java.lang.ExceptionInInitializerError
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1087)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.AbstractRequestLoggingFilter.doFilterInternal(AbstractRequestLoggingFilter.java:289)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.AbstractRequestLoggingFilter.doFilterInternal(AbstractRequestLoggingFilter.java:289)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.nrcp.trace.http.ServletFilter.doFilter(ServletFilter.java:63)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.openoa.common.config.XssFilter.doFilter(XssFilter.java:19)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.ExceptionInInitializerError: null
	at com.dianping.cat.message.internal.DefaultMessageManager.<init>(DefaultMessageManager.java:45)
	at com.dianping.cat.message.internal.DefaultMessageManager.<clinit>(DefaultMessageManager.java:52)
	at com.dianping.cat.message.internal.DefaultMessageProducer.<init>(DefaultMessageProducer.java:33)
	at com.dianping.cat.message.internal.DefaultMessageProducer.<clinit>(DefaultMessageProducer.java:37)
	at com.dianping.cat.Cat.initializeInternal(Cat.java:311)
	at com.dianping.cat.Cat.checkAndInitialize(Cat.java:72)
	at com.dianping.cat.Cat.getProducer(Cat.java:211)
	at com.dianping.cat.Cat.newTransaction(Cat.java:627)
	at org.openoa.aspect.WebAspect.around(WebAspect.java:52)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at org.openoa.controller.UserController$$EnhancerBySpringCGLIB$$f64dd110.queryByNameFuzzy(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	... 49 common frames omitted
Caused by: java.lang.RuntimeException: Error when get cat router service, please contact cat support team for help!
	at com.dianping.cat.configuration.ApplicationEnvironment.loadClientConfig(ApplicationEnvironment.java:151)
	at com.dianping.cat.configuration.DefaultClientConfigService.<init>(DefaultClientConfigService.java:72)
	at com.dianping.cat.configuration.DefaultClientConfigService.<clinit>(DefaultClientConfigService.java:52)
	... 83 common frames omitted
Caused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://org.cat/cat/s/launch?ip=**************&env=unknown&hostname=KyleHsudeMacBook-Pro.local
	at sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1932)
	at sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1520)
	at com.dianping.cat.util.Urls$UrlIO.openStream(Urls.java:83)
	at com.dianping.cat.util.Urls$UrlIO.openStream(Urls.java:59)
	at com.dianping.cat.util.NetworkHelper.readFromUrlWithRetry(NetworkHelper.java:36)
	at com.dianping.cat.configuration.ApplicationEnvironment.loadRemoteClientConfig(ApplicationEnvironment.java:168)
	at com.dianping.cat.configuration.ApplicationEnvironment.loadClientConfig(ApplicationEnvironment.java:132)
	... 85 common frames omitted
2025-08-27 14:05:37.153 INFO  [pool-4-thread-1] com.nrcp.log.ErrorLogSender$1.run:100 - [traceId:] ErrorLogSender send error log [pre 环境antflow项目 TraceId: 00000000.1756274736076.********* ip: ************** Exception: 全局异常捕获: {}
org.springframework.web.util.NestedServletException: Handler dispatch failed; nested exception is java.lang.ExceptionInInitializerError
at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1087)
at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
at org.springframework.web.filter.AbstractRequestLoggingFilter.doFilterInternal(AbstractRequestLoggingFilter.java:289)
at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
 argsList: ["Handler dispatch failed; nested exception is java.lang.ExceptionInInitializerError"]]
2025-08-27 14:05:37.155 ERROR [http-nio-7001-exec-1] org.openoa.common.config.mvc.GlobalExceptionHandler.handler:133 - [traceId:00000000.1756274736076.*********] http://localhost:7001/user/queryByNameFuzzy
org.springframework.web.util.NestedServletException: Handler dispatch failed; nested exception is java.lang.ExceptionInInitializerError
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1087)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.AbstractRequestLoggingFilter.doFilterInternal(AbstractRequestLoggingFilter.java:289)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.AbstractRequestLoggingFilter.doFilterInternal(AbstractRequestLoggingFilter.java:289)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.nrcp.trace.http.ServletFilter.doFilter(ServletFilter.java:63)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.openoa.common.config.XssFilter.doFilter(XssFilter.java:19)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.ExceptionInInitializerError: null
	at com.dianping.cat.message.internal.DefaultMessageManager.<init>(DefaultMessageManager.java:45)
	at com.dianping.cat.message.internal.DefaultMessageManager.<clinit>(DefaultMessageManager.java:52)
	at com.dianping.cat.message.internal.DefaultMessageProducer.<init>(DefaultMessageProducer.java:33)
	at com.dianping.cat.message.internal.DefaultMessageProducer.<clinit>(DefaultMessageProducer.java:37)
	at com.dianping.cat.Cat.initializeInternal(Cat.java:311)
	at com.dianping.cat.Cat.checkAndInitialize(Cat.java:72)
	at com.dianping.cat.Cat.getProducer(Cat.java:211)
	at com.dianping.cat.Cat.newTransaction(Cat.java:627)
	at org.openoa.aspect.WebAspect.around(WebAspect.java:52)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at org.openoa.controller.UserController$$EnhancerBySpringCGLIB$$f64dd110.queryByNameFuzzy(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	... 49 common frames omitted
Caused by: java.lang.RuntimeException: Error when get cat router service, please contact cat support team for help!
	at com.dianping.cat.configuration.ApplicationEnvironment.loadClientConfig(ApplicationEnvironment.java:151)
	at com.dianping.cat.configuration.DefaultClientConfigService.<init>(DefaultClientConfigService.java:72)
	at com.dianping.cat.configuration.DefaultClientConfigService.<clinit>(DefaultClientConfigService.java:52)
	... 83 common frames omitted
Caused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://org.cat/cat/s/launch?ip=**************&env=unknown&hostname=KyleHsudeMacBook-Pro.local
	at sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1932)
	at sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1520)
	at com.dianping.cat.util.Urls$UrlIO.openStream(Urls.java:83)
	at com.dianping.cat.util.Urls$UrlIO.openStream(Urls.java:59)
	at com.dianping.cat.util.NetworkHelper.readFromUrlWithRetry(NetworkHelper.java:36)
	at com.dianping.cat.configuration.ApplicationEnvironment.loadRemoteClientConfig(ApplicationEnvironment.java:168)
	at com.dianping.cat.configuration.ApplicationEnvironment.loadClientConfig(ApplicationEnvironment.java:132)
	... 85 common frames omitted
2025-08-27 14:05:37.458 INFO  [pool-4-thread-1] com.nrcp.log.CompanyWeChatAlarmService.sendAlarm:50 - [traceId:] alarm call wechat roobat response info [{"errcode":0,"errmsg":"ok"}]
2025-08-27 14:05:37.458 INFO  [pool-4-thread-1] com.nrcp.log.ErrorLogSender$1.run:100 - [traceId:] ErrorLogSender send error log [pre 环境antflow项目 TraceId: 00000000.1756274736076.********* ip: ************** Exception: http://localhost:7001/user/queryByNameFuzzy
org.springframework.web.util.NestedServletException: Handler dispatch failed; nested exception is java.lang.ExceptionInInitializerError
at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1087)
at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
at org.springframework.web.filter.AbstractRequestLoggingFilter.doFilterInternal(AbstractRequestLoggingFilter.java:289)
at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
 argsList: null]
2025-08-27 14:05:37.644 INFO  [pool-4-thread-1] com.nrcp.log.CompanyWeChatAlarmService.sendAlarm:50 - [traceId:] alarm call wechat roobat response info [{"errcode":0,"errmsg":"ok"}]
2025-08-27 14:06:09.188 INFO  [http-nio-7001-exec-2] org.openoa.aspect.AuthInterceptor.preHandle:25 - [traceId:00000000.1756274769020.*********] 员工=侯晓瑶, jt83326, 请求路径：/user/queryByNameFuzzy
2025-08-27 14:06:09.193 ERROR [http-nio-7001-exec-2] org.openoa.common.config.mvc.GlobalExceptionHandler.handler:43 - [traceId:00000000.1756274769020.*********] 全局异常捕获: Handler dispatch failed; nested exception is java.lang.NoClassDefFoundError: Could not initialize class com.dianping.cat.message.internal.DefaultMessageProducer
org.springframework.web.util.NestedServletException: Handler dispatch failed; nested exception is java.lang.NoClassDefFoundError: Could not initialize class com.dianping.cat.message.internal.DefaultMessageProducer
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1087)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.AbstractRequestLoggingFilter.doFilterInternal(AbstractRequestLoggingFilter.java:289)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.AbstractRequestLoggingFilter.doFilterInternal(AbstractRequestLoggingFilter.java:289)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.nrcp.trace.http.ServletFilter.doFilter(ServletFilter.java:63)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.openoa.common.config.XssFilter.doFilter(XssFilter.java:19)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.NoClassDefFoundError: Could not initialize class com.dianping.cat.message.internal.DefaultMessageProducer
	at com.dianping.cat.Cat.initializeInternal(Cat.java:311)
	at com.dianping.cat.Cat.checkAndInitialize(Cat.java:72)
	at com.dianping.cat.Cat.getProducer(Cat.java:211)
	at com.dianping.cat.Cat.newTransaction(Cat.java:627)
	at org.openoa.aspect.WebAspect.around(WebAspect.java:52)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at org.openoa.controller.UserController$$EnhancerBySpringCGLIB$$f64dd110.queryByNameFuzzy(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	... 49 common frames omitted
2025-08-27 14:06:09.193 INFO  [pool-4-thread-1] com.nrcp.log.ErrorLogSender$1.run:100 - [traceId:] ErrorLogSender send error log [pre 环境antflow项目 TraceId: 00000000.1756274769020.********* ip: ************** Exception: 全局异常捕获: {}
org.springframework.web.util.NestedServletException: Handler dispatch failed; nested exception is java.lang.NoClassDefFoundError: Could not initialize class com.dianping.cat.message.internal.DefaultMessageProducer
at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1087)
at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
at org.springframework.web.filter.AbstractRequestLoggingFilter.doFilterInternal(AbstractRequestLoggingFilter.java:289)
at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
 argsList: ["Handler dispatch failed; nested exception is java.lang.NoClassDefFoundError: Could not initialize class com.dianping.cat.message.internal.DefaultMessageProducer"]]
2025-08-27 14:06:09.194 ERROR [http-nio-7001-exec-2] org.openoa.common.config.mvc.GlobalExceptionHandler.handler:133 - [traceId:00000000.1756274769020.*********] http://localhost:7001/user/queryByNameFuzzy
org.springframework.web.util.NestedServletException: Handler dispatch failed; nested exception is java.lang.NoClassDefFoundError: Could not initialize class com.dianping.cat.message.internal.DefaultMessageProducer
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1087)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.AbstractRequestLoggingFilter.doFilterInternal(AbstractRequestLoggingFilter.java:289)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.AbstractRequestLoggingFilter.doFilterInternal(AbstractRequestLoggingFilter.java:289)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.nrcp.trace.http.ServletFilter.doFilter(ServletFilter.java:63)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.openoa.common.config.XssFilter.doFilter(XssFilter.java:19)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.NoClassDefFoundError: Could not initialize class com.dianping.cat.message.internal.DefaultMessageProducer
	at com.dianping.cat.Cat.initializeInternal(Cat.java:311)
	at com.dianping.cat.Cat.checkAndInitialize(Cat.java:72)
	at com.dianping.cat.Cat.getProducer(Cat.java:211)
	at com.dianping.cat.Cat.newTransaction(Cat.java:627)
	at org.openoa.aspect.WebAspect.around(WebAspect.java:52)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at org.openoa.controller.UserController$$EnhancerBySpringCGLIB$$f64dd110.queryByNameFuzzy(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	... 49 common frames omitted
2025-08-27 14:06:09.390 INFO  [pool-4-thread-1] com.nrcp.log.CompanyWeChatAlarmService.sendAlarm:50 - [traceId:] alarm call wechat roobat response info [{"errcode":0,"errmsg":"ok"}]
2025-08-27 14:06:09.391 INFO  [pool-4-thread-1] com.nrcp.log.ErrorLogSender$1.run:100 - [traceId:] ErrorLogSender send error log [pre 环境antflow项目 TraceId: 00000000.1756274769020.********* ip: ************** Exception: http://localhost:7001/user/queryByNameFuzzy
org.springframework.web.util.NestedServletException: Handler dispatch failed; nested exception is java.lang.NoClassDefFoundError: Could not initialize class com.dianping.cat.message.internal.DefaultMessageProducer
at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1087)
at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
at org.springframework.web.filter.AbstractRequestLoggingFilter.doFilterInternal(AbstractRequestLoggingFilter.java:289)
at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
 argsList: null]
2025-08-27 14:06:09.590 INFO  [pool-4-thread-1] com.nrcp.log.CompanyWeChatAlarmService.sendAlarm:50 - [traceId:] alarm call wechat roobat response info [{"errcode":0,"errmsg":"ok"}]
2025-08-27 14:07:51.744 INFO  [Thread-21] com.xxl.job.core.server.EmbedServer$1.run:96 - [traceId:] >>>>>>>>>>> xxl-job remoting server stop.
2025-08-27 14:07:51.841 INFO  [xxl-job, executor ExecutorRegistryThread] com.xxl.job.core.thread.ExecutorRegistryThread$1.run:87 - [traceId:] >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='antflow-pre', registryValue='http://**************:9421/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-08-27 14:07:51.842 INFO  [xxl-job, executor ExecutorRegistryThread] com.xxl.job.core.thread.ExecutorRegistryThread$1.run:105 - [traceId:] >>>>>>>>>>> xxl-job, executor registry thread destory.
2025-08-27 14:07:51.842 INFO  [SpringApplicationShutdownHook] com.xxl.job.core.server.EmbedServer.stop:125 - [traceId:] >>>>>>>>>>> xxl-job remoting server destroy success.
2025-08-27 14:07:51.842 INFO  [xxl-job, executor JobLogFileCleanThread] com.xxl.job.core.thread.JobLogFileCleanThread$1.run:99 - [traceId:] >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2025-08-27 14:07:51.842 INFO  [xxl-job, executor TriggerCallbackThread] com.xxl.job.core.thread.TriggerCallbackThread$1.run:97 - [traceId:] >>>>>>>>>>> xxl-job, executor callback thread destory.
2025-08-27 14:07:51.842 INFO  [Thread-20] com.xxl.job.core.thread.TriggerCallbackThread$2.run:127 - [traceId:] >>>>>>>>>>> xxl-job, executor retry callback thread destory.
2025-08-27 14:08:41.894 INFO  [main] org.springframework.boot.StartupInfoLogger.logStarting:55 - [traceId:] Starting AntFlowApplication using Java 1.8.0_432 on KyleHsudeMacBook-Pro.local with PID 45808 (/Users/<USER>/shuma/source-code/Antflow/antflow-web/target/classes started by kylehsu in /Users/<USER>/shuma/source-code/Antflow)
2025-08-27 14:08:41.895 INFO  [main] org.springframework.boot.SpringApplication.logStartupProfileInfo:638 - [traceId:] The following 1 profile is active: "pre"
2025-08-27 14:08:42.453 INFO  [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate.multipleStoresDetected:262 - [traceId:] Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-27 14:08:42.454 INFO  [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate.registerRepositoriesIn:132 - [traceId:] Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-27 14:08:42.544 INFO  [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate.registerRepositoriesIn:201 - [traceId:] Finished Spring Data repository scanning in 60 ms. Found 0 Redis repository interfaces.
2025-08-27 14:08:43.071 INFO  [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize:108 - [traceId:] Tomcat initialized with port(s): 7001 (http)
2025-08-27 14:08:43.076 INFO  [main] org.apache.juli.logging.DirectJDKLog.log:173 - [traceId:] Starting service [Tomcat]
2025-08-27 14:08:43.076 INFO  [main] org.apache.juli.logging.DirectJDKLog.log:173 - [traceId:] Starting Servlet engine: [Apache Tomcat/9.0.82]
2025-08-27 14:08:43.116 INFO  [main] org.apache.juli.logging.DirectJDKLog.log:173 - [traceId:] Initializing Spring embedded WebApplicationContext
2025-08-27 14:08:43.116 INFO  [main] org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.prepareWebApplicationContext:292 - [traceId:] Root WebApplicationContext: initialization completed in 1120 ms
2025-08-27 14:08:43.781 INFO  [main] com.zaxxer.hikari.HikariDataSource.getConnection:110 - [traceId:] HikariPool-1 - Starting...
2025-08-27 14:08:44.728 INFO  [main] com.zaxxer.hikari.HikariDataSource.getConnection:123 - [traceId:] HikariPool-1 - Start completed.
2025-08-27 14:08:44.956 INFO  [main] org.activiti.engine.impl.ProcessEngineImpl.<init>:86 - [traceId:] ProcessEngine default created
2025-08-27 14:08:44.957 INFO  [main] org.openoa.engine.bpmnconf.activitilistener.BpmnGlobalEventListener.onEvent:45 - [traceId:] AntFlow Engine is started
2025-08-27 14:08:44.957 INFO  [main] org.activiti.engine.impl.cfg.multitenant.MultiSchemaMultiTenantProcessEngineConfiguration.createTenantSchema:160 - [traceId:] creating/validating database schema for tenant 
2025-08-27 14:08:47.501 INFO  [main] org.openoa.base.dto.wecom.WechatConfigProperties.init:40 - [traceId:] ===> wechat config: WechatConfigProperties(zhongqi=WechatConfigProperties.WechatConfig(agentId=1000229, corpId=wwf1c477c862181a7f, corpsecret=IH5PGBOHD1dOMX86TOHi8UclM7uUUzaODKvbmE2m-ZA, approveDomain=test-approve.ceboss.cn), chenxing=WechatConfigProperties.WechatConfig(agentId=1000099, corpId=wwe2351ed80f0479d4, corpsecret=_PhZTbZXs6-zucZq1efQXWW5L3ya69RUkTgQRj0eWQk, approveDomain=test-approve.oristarcloud.com), xinwang=WechatConfigProperties.WechatConfig(agentId=1000129, corpId=wwd674e2ffdc4f6862, corpsecret=kG1XfR6fwFoPBRBIPev92WVw5QtOu2UB0AiNx0TluG4, approveDomain=test-approve.xinnet.com), kuajing=WechatConfigProperties.WechatConfig(agentId=1000060, corpId=ww8bd429f5b618a57e, corpsecret=SFO9FKx4G0Yl_y5E8Jw5cf0DJHmuVisRsOQouayXYkM, approveDomain=test-approve.gboss.tech), jituan=WechatConfigProperties.WechatConfig(agentId=1000008, corpId=wwa0a360c6341c5ae6, corpsecret=GnyB1Y1-SWkutF8_vIDeyGj3WsYqzuDTo9eB5kzJbOg, approveDomain=test-approve.ce-group.cn), smhg=WechatConfigProperties.WechatConfig(agentId=999999, corpId=999999, corpsecret=999999, approveDomain=999999), xiaoxia=WechatConfigProperties.WechatConfig(agentId=1000011, corpId=ww71746e109858902c, corpsecret=jAYDpVHoRihTuXLJ8Zm1HPV5M1t9XMi7-861mGdl4rA, approveDomain=test-approve.xiaoxiatech.com))
2025-08-27 14:08:48.427 INFO  [main] cn.ce.xxljob.config.XxlAdminAddressConfig.afterPropertiesSet:91 - [traceId:] 处理配置项: xxl.job.admin.addresses:http://pre-omo.aiyouyi.cn/xxl-job-admin/
2025-08-27 14:08:48.427 INFO  [main] cn.ce.xxljob.config.XxlAdminAddressConfig.afterPropertiesSet:109 - [traceId:] Xxl Job Master Admin URL: http://pre-omo.aiyouyi.cn/xxl-job-admin/
2025-08-27 14:08:48.429 INFO  [main] cn.ce.xxljob.config.XxlJobConfig.xxlJobExecutor:46 - [traceId:] >>>>>>>>>>> xxl-job config init.
2025-08-27 14:08:48.686 INFO  [main] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:168 - [traceId:] >>>>>>>>>>> xxl-job register jobhandler success, name:ANTFLOW_SYNC_EHR_EMP_INFO_JOB_HANDLER, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@337c0ee4[class org.openoa.job.SyncEhrDataJob#syncEhrEmpInfoJobHandler]
2025-08-27 14:08:48.687 INFO  [main] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:168 - [traceId:] >>>>>>>>>>> xxl-job register jobhandler success, name:ANTFLOW_SYNC_EHR_ORG_INFO_JOB_HANDLER, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@67b09e34[class org.openoa.job.SyncEhrDataJob#syncEhrOrgInfoJobHandler]
2025-08-27 14:08:48.773 INFO  [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer.start:220 - [traceId:] Tomcat started on port(s): 7001 (http) with context path ''
2025-08-27 14:08:48.779 INFO  [main] org.springframework.boot.StartupInfoLogger.logStarted:61 - [traceId:] Started AntFlowApplication in 7.329 seconds (JVM running for 7.707)
2025-08-27 14:08:48.780 INFO  [main] org.openoa.common.config.StartedListener.onApplicationEvent:25 - [traceId:] [94myour project server started at: http://127.0.0.1:7001[0;39m
2025-08-27 14:08:48.780 INFO  [main] org.openoa.common.config.StartedListener.onApplicationEvent:27 - [traceId:] [93myour project server was started successfully on port:7001 with context path:/[0;39m
2025-08-27 14:08:48.780 ERROR [main] org.openoa.AntFlowApplication.main:15 - [traceId:] AntFlow 项目启动成功!
2025-08-27 14:08:48.789 INFO  [Thread-9] com.xxl.job.core.server.EmbedServer$1.run:86 - [traceId:] >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9421
2025-08-27 14:08:48.802 INFO  [pool-4-thread-1] com.nrcp.log.ErrorLogSender$1.run:100 - [traceId:] ErrorLogSender send error log [pre 环境antflow项目 TraceId: null ip: ************** Exception: AntFlow 项目启动成功! argsList: null]
2025-08-27 14:39:10.080 INFO  [main] org.springframework.boot.StartupInfoLogger.logStarting:55 - [traceId:] Starting AntFlowApplication using Java 1.8.0_432 on KyleHsudeMacBook-Pro.local with PID 50183 (/Users/<USER>/shuma/source-code/Antflow/antflow-web/target/classes started by kylehsu in /Users/<USER>/shuma/source-code/Antflow)
2025-08-27 14:39:10.081 INFO  [main] org.springframework.boot.SpringApplication.logStartupProfileInfo:638 - [traceId:] The following 1 profile is active: "pre"
2025-08-27 14:39:10.783 INFO  [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate.multipleStoresDetected:262 - [traceId:] Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-27 14:39:10.784 INFO  [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate.registerRepositoriesIn:132 - [traceId:] Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-27 14:39:10.874 INFO  [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate.registerRepositoriesIn:201 - [traceId:] Finished Spring Data repository scanning in 59 ms. Found 0 Redis repository interfaces.
2025-08-27 14:39:11.439 INFO  [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize:108 - [traceId:] Tomcat initialized with port(s): 7001 (http)
2025-08-27 14:39:11.443 INFO  [main] org.apache.juli.logging.DirectJDKLog.log:173 - [traceId:] Starting service [Tomcat]
2025-08-27 14:39:11.443 INFO  [main] org.apache.juli.logging.DirectJDKLog.log:173 - [traceId:] Starting Servlet engine: [Apache Tomcat/9.0.82]
2025-08-27 14:39:11.485 INFO  [main] org.apache.juli.logging.DirectJDKLog.log:173 - [traceId:] Initializing Spring embedded WebApplicationContext
2025-08-27 14:39:11.486 INFO  [main] org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.prepareWebApplicationContext:292 - [traceId:] Root WebApplicationContext: initialization completed in 1287 ms
2025-08-27 14:39:12.159 INFO  [main] com.zaxxer.hikari.HikariDataSource.getConnection:110 - [traceId:] HikariPool-1 - Starting...
2025-08-27 14:39:12.926 INFO  [main] com.zaxxer.hikari.HikariDataSource.getConnection:123 - [traceId:] HikariPool-1 - Start completed.
2025-08-27 14:39:13.217 INFO  [main] org.activiti.engine.impl.ProcessEngineImpl.<init>:86 - [traceId:] ProcessEngine default created
2025-08-27 14:39:13.219 INFO  [main] org.openoa.engine.bpmnconf.activitilistener.BpmnGlobalEventListener.onEvent:45 - [traceId:] AntFlow Engine is started
2025-08-27 14:39:13.219 INFO  [main] org.activiti.engine.impl.cfg.multitenant.MultiSchemaMultiTenantProcessEngineConfiguration.createTenantSchema:160 - [traceId:] creating/validating database schema for tenant 
2025-08-27 14:39:15.832 INFO  [main] org.openoa.base.dto.wecom.WechatConfigProperties.init:40 - [traceId:] ===> wechat config: WechatConfigProperties(zhongqi=WechatConfigProperties.WechatConfig(agentId=1000229, corpId=wwf1c477c862181a7f, corpsecret=IH5PGBOHD1dOMX86TOHi8UclM7uUUzaODKvbmE2m-ZA, approveDomain=test-approve.ceboss.cn), chenxing=WechatConfigProperties.WechatConfig(agentId=1000099, corpId=wwe2351ed80f0479d4, corpsecret=_PhZTbZXs6-zucZq1efQXWW5L3ya69RUkTgQRj0eWQk, approveDomain=test-approve.oristarcloud.com), xinwang=WechatConfigProperties.WechatConfig(agentId=1000129, corpId=wwd674e2ffdc4f6862, corpsecret=kG1XfR6fwFoPBRBIPev92WVw5QtOu2UB0AiNx0TluG4, approveDomain=test-approve.xinnet.com), kuajing=WechatConfigProperties.WechatConfig(agentId=1000060, corpId=ww8bd429f5b618a57e, corpsecret=SFO9FKx4G0Yl_y5E8Jw5cf0DJHmuVisRsOQouayXYkM, approveDomain=test-approve.gboss.tech), jituan=WechatConfigProperties.WechatConfig(agentId=1000008, corpId=wwa0a360c6341c5ae6, corpsecret=GnyB1Y1-SWkutF8_vIDeyGj3WsYqzuDTo9eB5kzJbOg, approveDomain=test-approve.ce-group.cn), smhg=WechatConfigProperties.WechatConfig(agentId=999999, corpId=999999, corpsecret=999999, approveDomain=999999), xiaoxia=WechatConfigProperties.WechatConfig(agentId=1000011, corpId=ww71746e109858902c, corpsecret=jAYDpVHoRihTuXLJ8Zm1HPV5M1t9XMi7-861mGdl4rA, approveDomain=test-approve.xiaoxiatech.com))
2025-08-27 14:39:16.764 INFO  [main] cn.ce.xxljob.config.XxlAdminAddressConfig.afterPropertiesSet:91 - [traceId:] 处理配置项: xxl.job.admin.addresses:http://pre-omo.aiyouyi.cn/xxl-job-admin/
2025-08-27 14:39:16.764 INFO  [main] cn.ce.xxljob.config.XxlAdminAddressConfig.afterPropertiesSet:109 - [traceId:] Xxl Job Master Admin URL: http://pre-omo.aiyouyi.cn/xxl-job-admin/
2025-08-27 14:39:16.766 INFO  [main] cn.ce.xxljob.config.XxlJobConfig.xxlJobExecutor:46 - [traceId:] >>>>>>>>>>> xxl-job config init.
2025-08-27 14:39:17.090 INFO  [main] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:168 - [traceId:] >>>>>>>>>>> xxl-job register jobhandler success, name:ANTFLOW_SYNC_EHR_EMP_INFO_JOB_HANDLER, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@488279d3[class org.openoa.job.SyncEhrDataJob#syncEhrEmpInfoJobHandler]
2025-08-27 14:39:17.090 INFO  [main] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:168 - [traceId:] >>>>>>>>>>> xxl-job register jobhandler success, name:ANTFLOW_SYNC_EHR_ORG_INFO_JOB_HANDLER, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@27575bcd[class org.openoa.job.SyncEhrDataJob#syncEhrOrgInfoJobHandler]
2025-08-27 14:39:17.192 INFO  [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer.start:220 - [traceId:] Tomcat started on port(s): 7001 (http) with context path ''
2025-08-27 14:39:17.216 INFO  [Thread-20] com.xxl.job.core.server.EmbedServer$1.run:86 - [traceId:] >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9421
2025-08-27 14:39:18.294 INFO  [main] org.springframework.boot.StartupInfoLogger.logStarted:61 - [traceId:] Started AntFlowApplication in 8.652 seconds (JVM running for 9.065)
2025-08-27 14:39:18.296 INFO  [main] org.openoa.common.config.StartedListener.onApplicationEvent:25 - [traceId:] [94myour project server started at: http://127.0.0.1:7001[0;39m
2025-08-27 14:39:18.296 INFO  [main] org.openoa.common.config.StartedListener.onApplicationEvent:27 - [traceId:] [93myour project server was started successfully on port:7001 with context path:/[0;39m
2025-08-27 14:39:18.296 ERROR [main] org.openoa.AntFlowApplication.main:15 - [traceId:] AntFlow 项目启动成功!
2025-08-27 14:39:18.322 INFO  [pool-4-thread-1] com.nrcp.log.ErrorLogSender$1.run:100 - [traceId:] ErrorLogSender send error log [pre 环境antflow项目 TraceId: null ip: ************** Exception: AntFlow 项目启动成功! argsList: null]
2025-08-27 14:39:18.651 INFO  [pool-4-thread-1] com.nrcp.log.CompanyWeChatAlarmService.sendAlarm:50 - [traceId:] alarm call wechat roobat response info [{"errcode":0,"errmsg":"ok"}]
2025-08-27 14:46:44.893 INFO  [Thread-20] com.xxl.job.core.server.EmbedServer$1.run:96 - [traceId:] >>>>>>>>>>> xxl-job remoting server stop.
2025-08-27 14:46:45.016 INFO  [xxl-job, executor ExecutorRegistryThread] com.xxl.job.core.thread.ExecutorRegistryThread$1.run:87 - [traceId:] >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='antflow-pre', registryValue='http://**************:9421/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-08-27 14:46:45.017 INFO  [xxl-job, executor ExecutorRegistryThread] com.xxl.job.core.thread.ExecutorRegistryThread$1.run:105 - [traceId:] >>>>>>>>>>> xxl-job, executor registry thread destory.
2025-08-27 14:46:45.017 INFO  [SpringApplicationShutdownHook] com.xxl.job.core.server.EmbedServer.stop:125 - [traceId:] >>>>>>>>>>> xxl-job remoting server destroy success.
2025-08-27 14:46:45.018 INFO  [xxl-job, executor JobLogFileCleanThread] com.xxl.job.core.thread.JobLogFileCleanThread$1.run:99 - [traceId:] >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2025-08-27 14:46:45.018 INFO  [xxl-job, executor TriggerCallbackThread] com.xxl.job.core.thread.TriggerCallbackThread$1.run:97 - [traceId:] >>>>>>>>>>> xxl-job, executor callback thread destory.
2025-08-27 14:46:45.019 INFO  [Thread-19] com.xxl.job.core.thread.TriggerCallbackThread$2.run:127 - [traceId:] >>>>>>>>>>> xxl-job, executor retry callback thread destory.
2025-08-27 15:56:07.246 INFO  [main] org.springframework.boot.StartupInfoLogger.logStarting:55 - [traceId:] Starting AntFlowApplication using Java 1.8.0_432 on KyleHsudeMacBook-Pro.local with PID 61728 (/Users/<USER>/shuma/source-code/Antflow/antflow-web/target/classes started by kylehsu in /Users/<USER>/shuma/source-code/Antflow)
2025-08-27 15:56:07.247 INFO  [main] org.springframework.boot.SpringApplication.logStartupProfileInfo:638 - [traceId:] The following 1 profile is active: "release"
2025-08-27 15:56:07.815 INFO  [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate.multipleStoresDetected:262 - [traceId:] Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-27 15:56:07.816 INFO  [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate.registerRepositoriesIn:132 - [traceId:] Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-27 15:56:07.907 INFO  [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate.registerRepositoriesIn:201 - [traceId:] Finished Spring Data repository scanning in 62 ms. Found 0 Redis repository interfaces.
2025-08-27 15:56:08.470 INFO  [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize:108 - [traceId:] Tomcat initialized with port(s): 8080 (http)
2025-08-27 15:56:08.474 INFO  [main] org.apache.juli.logging.DirectJDKLog.log:173 - [traceId:] Starting service [Tomcat]
2025-08-27 15:56:08.474 INFO  [main] org.apache.juli.logging.DirectJDKLog.log:173 - [traceId:] Starting Servlet engine: [Apache Tomcat/9.0.82]
2025-08-27 15:56:08.512 INFO  [main] org.apache.juli.logging.DirectJDKLog.log:173 - [traceId:] Initializing Spring embedded WebApplicationContext
2025-08-27 15:56:08.512 INFO  [main] org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.prepareWebApplicationContext:292 - [traceId:] Root WebApplicationContext: initialization completed in 1162 ms
2025-08-27 15:56:08.572 ERROR [main] org.springframework.boot.web.embedded.tomcat.TomcatStarter.onStartup:61 - [traceId:] Error starting Tomcat context. Exception: org.springframework.beans.factory.UnsatisfiedDependencyException. Message: Error creating bean with name 'jimuFilterRegistrationBean' defined in class path resource [org/openoa/engine/conf/mvc/MVCConf.class]: Unsatisfied dependency expressed through method 'logHandlerFilter' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'webRequestLoggingFilter': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'afUserService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'syncEhrOrgInfoServiceImpl': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'syncEhrOrgInfoMapper' defined in file [/Users/<USER>/shuma/source-code/Antflow/antflow-base/target/classes/org/openoa/base/mapper/SyncEhrOrgInfoMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Unsatisfied dependency expressed through method 'sqlSessionFactory' parameter 0; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'tenantAwareDataSource' defined in class path resource [org/openoa/engine/conf/engineconfig/TenantAwareDataSourceConfig.class]: Unsatisfied dependency expressed through method 'tenantAwareDataSource' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'multiTenantInfoHolder' defined in file [/Users/<USER>/shuma/source-code/Antflow/antflow-engine/target/classes/org/openoa/engine/conf/engineconfig/MultiTenantInfoHolder.class]: Invocation of init method failed; nested exception is org.springframework.boot.autoconfigure.jdbc.DataSourceProperties$DataSourceBeanCreationException: Failed to determine a suitable driver class
2025-08-27 15:56:08.584 INFO  [main] org.apache.juli.logging.DirectJDKLog.log:173 - [traceId:] Stopping service [Tomcat]
2025-08-27 15:56:08.588 WARN  [main] org.springframework.context.support.AbstractApplicationContext.refresh:591 - [traceId:] Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Unable to start web server; nested exception is org.springframework.boot.web.server.WebServerException: Unable to start embedded Tomcat
2025-08-27 15:56:08.593 INFO  [main] org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener.logMessage:136 - [traceId:] 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-08-27 15:56:08.603 ERROR [main] org.springframework.boot.diagnostics.LoggingFailureAnalysisReporter.report:40 - [traceId:] 

***************************
APPLICATION FAILED TO START
***************************

Description:

Failed to configure a DataSource: 'url' attribute is not specified and no embedded datasource could be configured.

Reason: Failed to determine a suitable driver class


Action:

Consider the following:
	If you want an embedded database (H2, HSQL or Derby), please put it on the classpath.
	If you have database settings to be loaded from a particular profile you may need to activate it (the profiles release are currently active).

2025-08-27 15:56:31.534 INFO  [main] org.springframework.boot.StartupInfoLogger.logStarting:55 - [traceId:] Starting AntFlowApplication using Java 1.8.0_432 on KyleHsudeMacBook-Pro.local with PID 61789 (/Users/<USER>/shuma/source-code/Antflow/antflow-web/target/classes started by kylehsu in /Users/<USER>/shuma/source-code/Antflow)
2025-08-27 15:56:31.535 INFO  [main] org.springframework.boot.SpringApplication.logStartupProfileInfo:638 - [traceId:] The following 1 profile is active: "pre"
2025-08-27 15:56:32.075 INFO  [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate.multipleStoresDetected:262 - [traceId:] Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-27 15:56:32.076 INFO  [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate.registerRepositoriesIn:132 - [traceId:] Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-27 15:56:32.164 INFO  [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate.registerRepositoriesIn:201 - [traceId:] Finished Spring Data repository scanning in 59 ms. Found 0 Redis repository interfaces.
2025-08-27 15:56:32.684 INFO  [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize:108 - [traceId:] Tomcat initialized with port(s): 7001 (http)
2025-08-27 15:56:32.688 INFO  [main] org.apache.juli.logging.DirectJDKLog.log:173 - [traceId:] Starting service [Tomcat]
2025-08-27 15:56:32.689 INFO  [main] org.apache.juli.logging.DirectJDKLog.log:173 - [traceId:] Starting Servlet engine: [Apache Tomcat/9.0.82]
2025-08-27 15:56:32.726 INFO  [main] org.apache.juli.logging.DirectJDKLog.log:173 - [traceId:] Initializing Spring embedded WebApplicationContext
2025-08-27 15:56:32.727 INFO  [main] org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.prepareWebApplicationContext:292 - [traceId:] Root WebApplicationContext: initialization completed in 1095 ms
2025-08-27 15:56:33.386 INFO  [main] com.zaxxer.hikari.HikariDataSource.getConnection:110 - [traceId:] HikariPool-1 - Starting...
2025-08-27 15:56:34.246 INFO  [main] com.zaxxer.hikari.HikariDataSource.getConnection:123 - [traceId:] HikariPool-1 - Start completed.
2025-08-27 15:56:34.536 INFO  [main] org.activiti.engine.impl.ProcessEngineImpl.<init>:86 - [traceId:] ProcessEngine default created
2025-08-27 15:56:34.537 INFO  [main] org.openoa.engine.bpmnconf.activitilistener.BpmnGlobalEventListener.onEvent:45 - [traceId:] AntFlow Engine is started
2025-08-27 15:56:34.538 INFO  [main] org.activiti.engine.impl.cfg.multitenant.MultiSchemaMultiTenantProcessEngineConfiguration.createTenantSchema:160 - [traceId:] creating/validating database schema for tenant 
2025-08-27 15:56:37.553 INFO  [main] org.openoa.base.dto.wecom.WechatConfigProperties.init:40 - [traceId:] ===> wechat config: WechatConfigProperties(zhongqi=WechatConfigProperties.WechatConfig(agentId=1000229, corpId=wwf1c477c862181a7f, corpsecret=IH5PGBOHD1dOMX86TOHi8UclM7uUUzaODKvbmE2m-ZA), chenxing=WechatConfigProperties.WechatConfig(agentId=1000099, corpId=wwe2351ed80f0479d4, corpsecret=_PhZTbZXs6-zucZq1efQXWW5L3ya69RUkTgQRj0eWQk), xinwang=WechatConfigProperties.WechatConfig(agentId=1000129, corpId=wwd674e2ffdc4f6862, corpsecret=kG1XfR6fwFoPBRBIPev92WVw5QtOu2UB0AiNx0TluG4), kuajing=WechatConfigProperties.WechatConfig(agentId=1000060, corpId=ww8bd429f5b618a57e, corpsecret=SFO9FKx4G0Yl_y5E8Jw5cf0DJHmuVisRsOQouayXYkM), jituan=WechatConfigProperties.WechatConfig(agentId=1000008, corpId=wwa0a360c6341c5ae6, corpsecret=GnyB1Y1-SWkutF8_vIDeyGj3WsYqzuDTo9eB5kzJbOg), smhg=WechatConfigProperties.WechatConfig(agentId=999999, corpId=999999, corpsecret=999999), xiaoxia=WechatConfigProperties.WechatConfig(agentId=1000011, corpId=ww71746e109858902c, corpsecret=jAYDpVHoRihTuXLJ8Zm1HPV5M1t9XMi7-861mGdl4rA))
2025-08-27 15:56:38.519 INFO  [main] cn.ce.xxljob.config.XxlAdminAddressConfig.afterPropertiesSet:91 - [traceId:] 处理配置项: xxl.job.admin.addresses:http://pre-omo.aiyouyi.cn/xxl-job-admin/
2025-08-27 15:56:38.519 INFO  [main] cn.ce.xxljob.config.XxlAdminAddressConfig.afterPropertiesSet:109 - [traceId:] Xxl Job Master Admin URL: http://pre-omo.aiyouyi.cn/xxl-job-admin/
2025-08-27 15:56:38.521 INFO  [main] cn.ce.xxljob.config.XxlJobConfig.xxlJobExecutor:46 - [traceId:] >>>>>>>>>>> xxl-job config init.
2025-08-27 15:56:38.839 INFO  [main] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:168 - [traceId:] >>>>>>>>>>> xxl-job register jobhandler success, name:ANTFLOW_SYNC_EHR_EMP_INFO_JOB_HANDLER, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4b360a82[class org.openoa.job.SyncEhrDataJob#syncEhrEmpInfoJobHandler]
2025-08-27 15:56:38.840 INFO  [main] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:168 - [traceId:] >>>>>>>>>>> xxl-job register jobhandler success, name:ANTFLOW_SYNC_EHR_ORG_INFO_JOB_HANDLER, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@75fa9254[class org.openoa.job.SyncEhrDataJob#syncEhrOrgInfoJobHandler]
2025-08-27 15:56:38.932 INFO  [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer.start:220 - [traceId:] Tomcat started on port(s): 7001 (http) with context path ''
2025-08-27 15:56:38.954 INFO  [Thread-20] com.xxl.job.core.server.EmbedServer$1.run:86 - [traceId:] >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9421
2025-08-27 15:56:40.050 INFO  [main] org.springframework.boot.StartupInfoLogger.logStarted:61 - [traceId:] Started AntFlowApplication in 8.946 seconds (JVM running for 9.288)
2025-08-27 15:56:40.052 INFO  [main] org.openoa.common.config.StartedListener.onApplicationEvent:25 - [traceId:] [94myour project server started at: http://127.0.0.1:7001[0;39m
2025-08-27 15:56:40.052 INFO  [main] org.openoa.common.config.StartedListener.onApplicationEvent:27 - [traceId:] [93myour project server was started successfully on port:7001 with context path:/[0;39m
2025-08-27 15:56:40.052 ERROR [main] org.openoa.AntFlowApplication.main:15 - [traceId:] AntFlow 项目启动成功!
2025-08-27 15:56:42.387 INFO  [http-nio-7001-exec-1] org.apache.juli.logging.DirectJDKLog.log:173 - [traceId:] Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-27 15:56:42.387 INFO  [http-nio-7001-exec-1] org.springframework.web.servlet.FrameworkServlet.initServletBean:525 - [traceId:] Initializing Servlet 'dispatcherServlet'
2025-08-27 15:56:42.388 INFO  [http-nio-7001-exec-1] org.springframework.web.servlet.FrameworkServlet.initServletBean:547 - [traceId:] Completed initialization in 1 ms
2025-08-27 15:56:42.618 INFO  [http-nio-7001-exec-1] org.openoa.aspect.AuthInterceptor.preHandle:25 - [traceId:00000000.1756281402404.617891000] 员工=侯晓瑶, jt83326, 请求路径：/bpmnConf/process/buttonsOperation
2025-08-27 15:56:42.694 INFO  [http-nio-7001-exec-1] org.openoa.engine.conf.aspect.DoButtonOperationAspect.around:112 - [traceId:00000000.1756281402404.617891000] params:{"taskId":"9db02b8f-8317-11f0-b69a-525400b07213","processNumber":"MB0659_8648946514117222400","formCode":"MB0659","isOutSideAccessProc":"false","outSideType":2,"isLowCodeFlow":"true","lfFields":{"input109445515":"11"},"approvalComment":"同意","operationType":3},formCode:MB0659
2025-08-27 15:56:54.414 INFO  [jimu-Async-Executor-1] org.openoa.engine.bpmnconf.service.biz.NotifyServiceImpl.notice:450 - [traceId:00000000.1756281402404.617891000] 发起人ID=jt83326, 消息接收人=[jt83326], 发送通知：WxMsgMarkdown(msgtype=markdown, markdown=WxMsgMarkdown.Markdown(content=您的**发起人自己审批测试v1**申请单发生了新的流转，请前往PC端查看详情~ 
))
2025-08-27 15:56:54.416 INFO  [jimu-Async-Executor-2] org.openoa.base.service.wecom.EhrSyncService.getEmployeeInfoByBusinessIds:90 - [traceId:00000000.1756281402404.617891000] 调用主数据系统，根据员工id获取所有记录，参数为:{"empIds":"jt83326"}
2025-08-27 15:56:54.416 INFO  [jimu-Async-Executor-2] org.openoa.base.service.wecom.GatewayApiImpl.request:40 - [traceId:00000000.1756281402404.617891000] 网关接口请求参数：url=http://10.24.49.198:8080/ehrData/sync/getEmployeesByEmpId,bodymap={empIds=[jt83326]},headers=[Content-Type:"application/x-www-form-urlencoded", X-SERIALNO:"null", traceId:"00000000.1756281402404.617891000", user_name:"null"]
2025-08-27 15:56:54.518 INFO  [jimu-Async-Executor-2] org.openoa.engine.conf.config.RestInterceptor.intermediateProcessRequest:52 - [traceId:00000000.1756281402404.617891000] request method : POST request body : empIds=jt83326
2025-08-27 15:56:54.520 INFO  [jimu-Async-Executor-2] org.openoa.base.service.wecom.EhrSyncService.getEmployeeInfoByBusinessIds:92 - [traceId:00000000.1756281402404.617891000] 调用主数据系统，根据员工id获取所有记录，结果为:{"msg":"请求成功","code":"200","data":[{"id":300748,"ehrSource":0,"empId":"83326","businessId":"jt83326","isLogin":0,"name":"侯晓瑶","orgId":"3891","orgName":"中企集团/产研中心/大数据与数字化部/公共运维","workMail":"<EMAIL>","officePhone":"010-87127449","mobile":"***********","state":1,"sex":"女","jobGrade":"研发族/0/0/P5(P)","pname":"功能测试工程师","enterDate":"2020-03-30 00:00:00","outDate":null,"jobId":846,"isPass":"Y","passDate":"2020-09-30 00:00:00","hrbrId":3891,"birthday":"1994-05-15 00:00:00","salaryOrgId":"9","loginPwd":null,"updateTime":"2025-08-01 22:49:23"}]}
2025-08-27 15:56:54.879 INFO  [jimu-Async-Executor-2] org.openoa.engine.conf.config.RestInterceptor.intermediateProcessRequest:52 - [traceId:00000000.1756281402404.617891000] request method : GET request body : null
2025-08-27 15:56:54.891 ERROR [jimu-Async-Executor-2] org.openoa.base.service.wecom.WechatService.lambda$sendWxMsgByBusinessId$4:110 - [traceId:00000000.1756281402404.617891000] 发送企业微信消息内容:{"agentid":"1000008","duplicate_check_interval":1800,"enable_duplicate_check":0,"enable_id_trans":0,"markdown":{"content":"您的**发起人自己审批测试v1**申请单发生了新的流转，请前往PC端查看详情~ \n"},"msgtype":"markdown","safe":0,"touser":"83326"}
2025-08-27 15:56:54.890 ERROR [http-nio-7001-exec-1] org.openoa.common.config.mvc.GlobalExceptionHandler.handler:43 - [traceId:00000000.1756281402404.617891000] 全局异常捕获: 当前流程已审批！
org.openoa.base.exception.JiMuBizException: 当前流程已审批！
	at org.openoa.engine.bpmnconf.service.biz.ResubmitProcessImpl.doProcessButton(ResubmitProcessImpl.java:76)
	at org.openoa.engine.bpmnconf.service.biz.ResubmitProcessImpl$$FastClassBySpringCGLIB$$73e0dab9.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:102)
	at org.openoa.engine.conf.aspect.BpmnSendMessageAspect.doMethod(BpmnSendMessageAspect.java:267)
	at org.openoa.engine.conf.aspect.BpmnSendMessageAspect.around(BpmnSendMessageAspect.java:147)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at org.openoa.engine.bpmnconf.service.biz.ResubmitProcessImpl$$EnhancerBySpringCGLIB$$38a48a05.doProcessButton(<generated>)
	at org.openoa.engine.bpmnconf.service.biz.ButtonOperationServiceImpl.buttonsOperationTransactional(ButtonOperationServiceImpl.java:64)
	at org.openoa.engine.bpmnconf.service.biz.ButtonOperationServiceImpl$$FastClassBySpringCGLIB$$6d2394ab.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at org.openoa.engine.bpmnconf.service.biz.ButtonOperationServiceImpl$$EnhancerBySpringCGLIB$$73aeac05.buttonsOperationTransactional(<generated>)
	at org.openoa.engine.conf.aspect.DoButtonOperationAspect.around(DoButtonOperationAspect.java:209)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at org.openoa.engine.factory.$Proxy_ButtonPreOperationServiceImpl$$EnhancerBySpringCGLIB$$d3710ef0.buttonsPreOperation(<generated>)
	at org.openoa.engine.bpmnconf.service.biz.ProcessApprovalServiceImpl.buttonsOperation(ProcessApprovalServiceImpl.java:84)
	at org.openoa.engine.bpmnconf.service.biz.ProcessApprovalServiceImpl$$FastClassBySpringCGLIB$$576d310e.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386)
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703)
	at org.openoa.engine.bpmnconf.service.biz.ProcessApprovalServiceImpl$$EnhancerBySpringCGLIB$$c492d70e.buttonsOperation(<generated>)
	at org.openoa.engine.bpmnconf.controller.BpmnConfController.buttonsOperation(BpmnConfController.java:257)
	at org.openoa.engine.bpmnconf.controller.BpmnConfController$$FastClassBySpringCGLIB$$761ac06a.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at org.openoa.engine.conf.aspect.HttpLogAspect.around(HttpLogAspect.java:48)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at org.openoa.engine.bpmnconf.controller.BpmnConfController$$EnhancerBySpringCGLIB$$124e431c.buttonsOperation(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.AbstractRequestLoggingFilter.doFilterInternal(AbstractRequestLoggingFilter.java:289)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.AbstractRequestLoggingFilter.doFilterInternal(AbstractRequestLoggingFilter.java:289)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.nrcp.trace.http.ServletFilter.doFilter(ServletFilter.java:63)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.openoa.common.config.XssFilter.doFilter(XssFilter.java:19)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
2025-08-27 15:56:55.181 INFO  [jimu-Async-Executor-2] org.openoa.engine.conf.config.RestInterceptor.intermediateProcessRequest:52 - [traceId:00000000.1756281402404.617891000] request method : POST request body : {"agentid":"1000008","duplicate_check_interval":1800,"enable_duplicate_check":0,"enable_id_trans":0,"markdown":{"content":"您的**发起人自己审批测试v1**申请单发生了新的流转，请前往PC端查看详情~ \n"},"msgtype":"markdown","safe":0,"touser":"83326"}
2025-08-27 16:02:25.996 INFO  [main] org.springframework.boot.StartupInfoLogger.logStarting:55 - [traceId:] Starting AntFlowApplication using Java 1.8.0_432 on KyleHsudeMacBook-Pro.local with PID 62732 (/Users/<USER>/shuma/source-code/Antflow/antflow-web/target/classes started by kylehsu in /Users/<USER>/shuma/source-code/Antflow)
2025-08-27 16:02:25.996 INFO  [main] org.springframework.boot.SpringApplication.logStartupProfileInfo:638 - [traceId:] The following 1 profile is active: "pre"
2025-08-27 16:02:26.550 INFO  [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate.multipleStoresDetected:262 - [traceId:] Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-27 16:02:26.551 INFO  [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate.registerRepositoriesIn:132 - [traceId:] Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-27 16:02:26.641 INFO  [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate.registerRepositoriesIn:201 - [traceId:] Finished Spring Data repository scanning in 60 ms. Found 0 Redis repository interfaces.
2025-08-27 16:02:27.168 INFO  [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize:108 - [traceId:] Tomcat initialized with port(s): 7001 (http)
2025-08-27 16:02:27.171 INFO  [main] org.apache.juli.logging.DirectJDKLog.log:173 - [traceId:] Starting service [Tomcat]
2025-08-27 16:02:27.171 INFO  [main] org.apache.juli.logging.DirectJDKLog.log:173 - [traceId:] Starting Servlet engine: [Apache Tomcat/9.0.82]
2025-08-27 16:02:27.211 INFO  [main] org.apache.juli.logging.DirectJDKLog.log:173 - [traceId:] Initializing Spring embedded WebApplicationContext
2025-08-27 16:02:27.211 INFO  [main] org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.prepareWebApplicationContext:292 - [traceId:] Root WebApplicationContext: initialization completed in 1116 ms
2025-08-27 16:02:27.869 INFO  [main] com.zaxxer.hikari.HikariDataSource.getConnection:110 - [traceId:] HikariPool-1 - Starting...
2025-08-27 16:02:28.620 INFO  [main] com.zaxxer.hikari.HikariDataSource.getConnection:123 - [traceId:] HikariPool-1 - Start completed.
2025-08-27 16:02:28.903 INFO  [main] org.activiti.engine.impl.ProcessEngineImpl.<init>:86 - [traceId:] ProcessEngine default created
2025-08-27 16:02:28.904 INFO  [main] org.openoa.engine.bpmnconf.activitilistener.BpmnGlobalEventListener.onEvent:45 - [traceId:] AntFlow Engine is started
2025-08-27 16:02:28.905 INFO  [main] org.activiti.engine.impl.cfg.multitenant.MultiSchemaMultiTenantProcessEngineConfiguration.createTenantSchema:160 - [traceId:] creating/validating database schema for tenant 
2025-08-27 16:02:32.197 INFO  [main] org.openoa.base.dto.wecom.WechatConfigProperties.init:40 - [traceId:] ===> wechat config: WechatConfigProperties(zhongqi=WechatConfigProperties.WechatConfig(agentId=1000229, corpId=wwf1c477c862181a7f, corpsecret=IH5PGBOHD1dOMX86TOHi8UclM7uUUzaODKvbmE2m-ZA, approveDomain=test-approve.ceboss.cn), chenxing=WechatConfigProperties.WechatConfig(agentId=1000099, corpId=wwe2351ed80f0479d4, corpsecret=_PhZTbZXs6-zucZq1efQXWW5L3ya69RUkTgQRj0eWQk, approveDomain=test-approve.oristarcloud.com), xinwang=WechatConfigProperties.WechatConfig(agentId=1000129, corpId=wwd674e2ffdc4f6862, corpsecret=kG1XfR6fwFoPBRBIPev92WVw5QtOu2UB0AiNx0TluG4, approveDomain=test-approve.xinnet.com), kuajing=WechatConfigProperties.WechatConfig(agentId=1000060, corpId=ww8bd429f5b618a57e, corpsecret=SFO9FKx4G0Yl_y5E8Jw5cf0DJHmuVisRsOQouayXYkM, approveDomain=test-approve.gboss.tech), jituan=WechatConfigProperties.WechatConfig(agentId=1000008, corpId=wwa0a360c6341c5ae6, corpsecret=GnyB1Y1-SWkutF8_vIDeyGj3WsYqzuDTo9eB5kzJbOg, approveDomain=test-approve.ce-group.cn), smhg=WechatConfigProperties.WechatConfig(agentId=999999, corpId=999999, corpsecret=999999, approveDomain=999999), xiaoxia=WechatConfigProperties.WechatConfig(agentId=1000011, corpId=ww71746e109858902c, corpsecret=jAYDpVHoRihTuXLJ8Zm1HPV5M1t9XMi7-861mGdl4rA, approveDomain=test-approve.xiaoxiatech.com))
2025-08-27 16:02:33.158 INFO  [main] cn.ce.xxljob.config.XxlAdminAddressConfig.afterPropertiesSet:91 - [traceId:] 处理配置项: xxl.job.admin.addresses:http://pre-omo.aiyouyi.cn/xxl-job-admin/
2025-08-27 16:02:33.158 INFO  [main] cn.ce.xxljob.config.XxlAdminAddressConfig.afterPropertiesSet:109 - [traceId:] Xxl Job Master Admin URL: http://pre-omo.aiyouyi.cn/xxl-job-admin/
2025-08-27 16:02:33.160 INFO  [main] cn.ce.xxljob.config.XxlJobConfig.xxlJobExecutor:46 - [traceId:] >>>>>>>>>>> xxl-job config init.
2025-08-27 16:02:33.480 INFO  [main] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:168 - [traceId:] >>>>>>>>>>> xxl-job register jobhandler success, name:ANTFLOW_SYNC_EHR_EMP_INFO_JOB_HANDLER, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6ad7a305[class org.openoa.job.SyncEhrDataJob#syncEhrEmpInfoJobHandler]
2025-08-27 16:02:33.481 INFO  [main] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:168 - [traceId:] >>>>>>>>>>> xxl-job register jobhandler success, name:ANTFLOW_SYNC_EHR_ORG_INFO_JOB_HANDLER, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@2fc435e9[class org.openoa.job.SyncEhrDataJob#syncEhrOrgInfoJobHandler]
2025-08-27 16:02:33.571 INFO  [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer.start:220 - [traceId:] Tomcat started on port(s): 7001 (http) with context path ''
2025-08-27 16:02:33.591 INFO  [Thread-21] com.xxl.job.core.server.EmbedServer$1.run:86 - [traceId:] >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9421
2025-08-27 16:02:34.656 INFO  [main] org.springframework.boot.StartupInfoLogger.logStarted:61 - [traceId:] Started AntFlowApplication in 9.105 seconds (JVM running for 9.466)
2025-08-27 16:02:34.658 INFO  [main] org.openoa.common.config.StartedListener.onApplicationEvent:25 - [traceId:] [94myour project server started at: http://127.0.0.1:7001[0;39m
2025-08-27 16:02:34.659 INFO  [main] org.openoa.common.config.StartedListener.onApplicationEvent:27 - [traceId:] [93myour project server was started successfully on port:7001 with context path:/[0;39m
2025-08-27 16:02:34.659 ERROR [main] org.openoa.AntFlowApplication.main:15 - [traceId:] AntFlow 项目启动成功!
2025-08-27 16:02:34.685 INFO  [pool-4-thread-1] com.nrcp.log.ErrorLogSender$1.run:100 - [traceId:] ErrorLogSender send error log [pre 环境antflow项目 TraceId: null ip: 127.0.0.1 Exception: AntFlow 项目启动成功! argsList: null]
2025-08-27 16:02:35.053 INFO  [pool-4-thread-1] com.nrcp.log.CompanyWeChatAlarmService.sendAlarm:50 - [traceId:] alarm call wechat roobat response info [{"errcode":0,"errmsg":"ok"}]
2025-08-27 16:02:53.077 INFO  [http-nio-7001-exec-1] org.apache.juli.logging.DirectJDKLog.log:173 - [traceId:] Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-27 16:02:53.077 INFO  [http-nio-7001-exec-1] org.springframework.web.servlet.FrameworkServlet.initServletBean:525 - [traceId:] Initializing Servlet 'dispatcherServlet'
2025-08-27 16:02:53.079 INFO  [http-nio-7001-exec-1] org.springframework.web.servlet.FrameworkServlet.initServletBean:547 - [traceId:] Completed initialization in 2 ms
2025-08-27 16:02:53.323 INFO  [http-nio-7001-exec-1] org.openoa.aspect.AuthInterceptor.preHandle:25 - [traceId:00000000.1756281773092.*********] 员工=侯晓瑶, jt83326, 请求路径：/user/queryByNameFuzzy
2025-08-27 16:02:57.372 ERROR [http-nio-7001-exec-1] org.openoa.common.config.mvc.GlobalExceptionHandler.handler:43 - [traceId:00000000.1756281773092.*********] 全局异常捕获: Handler dispatch failed; nested exception is java.lang.ExceptionInInitializerError
org.springframework.web.util.NestedServletException: Handler dispatch failed; nested exception is java.lang.ExceptionInInitializerError
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1087)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.AbstractRequestLoggingFilter.doFilterInternal(AbstractRequestLoggingFilter.java:289)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.AbstractRequestLoggingFilter.doFilterInternal(AbstractRequestLoggingFilter.java:289)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.nrcp.trace.http.ServletFilter.doFilter(ServletFilter.java:63)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.openoa.common.config.XssFilter.doFilter(XssFilter.java:19)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.ExceptionInInitializerError: null
	at com.dianping.cat.message.internal.DefaultMessageManager.<init>(DefaultMessageManager.java:45)
	at com.dianping.cat.message.internal.DefaultMessageManager.<clinit>(DefaultMessageManager.java:52)
	at com.dianping.cat.message.internal.DefaultMessageProducer.<init>(DefaultMessageProducer.java:33)
	at com.dianping.cat.message.internal.DefaultMessageProducer.<clinit>(DefaultMessageProducer.java:37)
	at com.dianping.cat.Cat.initializeInternal(Cat.java:311)
	at com.dianping.cat.Cat.checkAndInitialize(Cat.java:72)
	at com.dianping.cat.Cat.getProducer(Cat.java:211)
	at com.dianping.cat.Cat.newTransaction(Cat.java:627)
	at org.openoa.aspect.WebAspect.around(WebAspect.java:52)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at org.openoa.controller.UserController$$EnhancerBySpringCGLIB$$8ae4edad.queryByNameFuzzy(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	... 49 common frames omitted
Caused by: java.lang.RuntimeException: Error when get cat router service, please contact cat support team for help!
	at com.dianping.cat.configuration.ApplicationEnvironment.loadClientConfig(ApplicationEnvironment.java:151)
	at com.dianping.cat.configuration.DefaultClientConfigService.<init>(DefaultClientConfigService.java:72)
	at com.dianping.cat.configuration.DefaultClientConfigService.<clinit>(DefaultClientConfigService.java:52)
	... 83 common frames omitted
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)
	at java.net.SocketInputStream.read(SocketInputStream.java:171)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at java.io.BufferedInputStream.fill(BufferedInputStream.java:246)
	at java.io.BufferedInputStream.read1(BufferedInputStream.java:286)
	at java.io.BufferedInputStream.read(BufferedInputStream.java:345)
	at sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:745)
	at sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:680)
	at sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1615)
	at sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1520)
	at com.dianping.cat.util.Urls$UrlIO.openStream(Urls.java:83)
	at com.dianping.cat.util.Urls$UrlIO.openStream(Urls.java:59)
	at com.dianping.cat.util.NetworkHelper.readFromUrlWithRetry(NetworkHelper.java:36)
	at com.dianping.cat.configuration.ApplicationEnvironment.loadRemoteClientConfig(ApplicationEnvironment.java:168)
	at com.dianping.cat.configuration.ApplicationEnvironment.loadClientConfig(ApplicationEnvironment.java:132)
	... 85 common frames omitted
2025-08-27 16:02:57.372 INFO  [pool-4-thread-1] com.nrcp.log.ErrorLogSender$1.run:100 - [traceId:] ErrorLogSender send error log [pre 环境antflow项目 TraceId: 00000000.1756281773092.********* ip: 127.0.0.1 Exception: 全局异常捕获: {}
org.springframework.web.util.NestedServletException: Handler dispatch failed; nested exception is java.lang.ExceptionInInitializerError
at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1087)
at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
at org.springframework.web.filter.AbstractRequestLoggingFilter.doFilterInternal(AbstractRequestLoggingFilter.java:289)
at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
 argsList: ["Handler dispatch failed; nested exception is java.lang.ExceptionInInitializerError"]]
2025-08-27 16:02:57.375 ERROR [http-nio-7001-exec-1] org.openoa.common.config.mvc.GlobalExceptionHandler.handler:133 - [traceId:00000000.1756281773092.*********] http://localhost:7001/user/queryByNameFuzzy
org.springframework.web.util.NestedServletException: Handler dispatch failed; nested exception is java.lang.ExceptionInInitializerError
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1087)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.AbstractRequestLoggingFilter.doFilterInternal(AbstractRequestLoggingFilter.java:289)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.AbstractRequestLoggingFilter.doFilterInternal(AbstractRequestLoggingFilter.java:289)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.nrcp.trace.http.ServletFilter.doFilter(ServletFilter.java:63)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.openoa.common.config.XssFilter.doFilter(XssFilter.java:19)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.ExceptionInInitializerError: null
	at com.dianping.cat.message.internal.DefaultMessageManager.<init>(DefaultMessageManager.java:45)
	at com.dianping.cat.message.internal.DefaultMessageManager.<clinit>(DefaultMessageManager.java:52)
	at com.dianping.cat.message.internal.DefaultMessageProducer.<init>(DefaultMessageProducer.java:33)
	at com.dianping.cat.message.internal.DefaultMessageProducer.<clinit>(DefaultMessageProducer.java:37)
	at com.dianping.cat.Cat.initializeInternal(Cat.java:311)
	at com.dianping.cat.Cat.checkAndInitialize(Cat.java:72)
	at com.dianping.cat.Cat.getProducer(Cat.java:211)
	at com.dianping.cat.Cat.newTransaction(Cat.java:627)
	at org.openoa.aspect.WebAspect.around(WebAspect.java:52)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at org.openoa.controller.UserController$$EnhancerBySpringCGLIB$$8ae4edad.queryByNameFuzzy(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	... 49 common frames omitted
Caused by: java.lang.RuntimeException: Error when get cat router service, please contact cat support team for help!
	at com.dianping.cat.configuration.ApplicationEnvironment.loadClientConfig(ApplicationEnvironment.java:151)
	at com.dianping.cat.configuration.DefaultClientConfigService.<init>(DefaultClientConfigService.java:72)
	at com.dianping.cat.configuration.DefaultClientConfigService.<clinit>(DefaultClientConfigService.java:52)
	... 83 common frames omitted
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)
	at java.net.SocketInputStream.read(SocketInputStream.java:171)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at java.io.BufferedInputStream.fill(BufferedInputStream.java:246)
	at java.io.BufferedInputStream.read1(BufferedInputStream.java:286)
	at java.io.BufferedInputStream.read(BufferedInputStream.java:345)
	at sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:745)
	at sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:680)
	at sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1615)
	at sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1520)
	at com.dianping.cat.util.Urls$UrlIO.openStream(Urls.java:83)
	at com.dianping.cat.util.Urls$UrlIO.openStream(Urls.java:59)
	at com.dianping.cat.util.NetworkHelper.readFromUrlWithRetry(NetworkHelper.java:36)
	at com.dianping.cat.configuration.ApplicationEnvironment.loadRemoteClientConfig(ApplicationEnvironment.java:168)
	at com.dianping.cat.configuration.ApplicationEnvironment.loadClientConfig(ApplicationEnvironment.java:132)
	... 85 common frames omitted
2025-08-27 16:02:57.569 INFO  [pool-4-thread-1] com.nrcp.log.CompanyWeChatAlarmService.sendAlarm:50 - [traceId:] alarm call wechat roobat response info [{"errcode":0,"errmsg":"ok"}]
2025-08-27 16:02:57.569 INFO  [pool-4-thread-1] com.nrcp.log.ErrorLogSender$1.run:100 - [traceId:] ErrorLogSender send error log [pre 环境antflow项目 TraceId: 00000000.1756281773092.********* ip: 127.0.0.1 Exception: http://localhost:7001/user/queryByNameFuzzy
org.springframework.web.util.NestedServletException: Handler dispatch failed; nested exception is java.lang.ExceptionInInitializerError
at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1087)
at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
at org.springframework.web.filter.AbstractRequestLoggingFilter.doFilterInternal(AbstractRequestLoggingFilter.java:289)
at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
 argsList: null]
2025-08-27 16:02:57.781 INFO  [pool-4-thread-1] com.nrcp.log.CompanyWeChatAlarmService.sendAlarm:50 - [traceId:] alarm call wechat roobat response info [{"errcode":0,"errmsg":"ok"}]
2025-08-27 16:03:32.336 INFO  [http-nio-7001-exec-3] org.openoa.aspect.AuthInterceptor.preHandle:25 - [traceId:00000000.1756281812169.*********] 员工=侯晓瑶, jt83326, 请求路径：/user/queryByNameFuzzy
2025-08-27 16:03:32.341 ERROR [http-nio-7001-exec-3] org.openoa.common.config.mvc.GlobalExceptionHandler.handler:43 - [traceId:00000000.1756281812169.*********] 全局异常捕获: Handler dispatch failed; nested exception is java.lang.NoClassDefFoundError: Could not initialize class com.dianping.cat.message.internal.DefaultMessageProducer
org.springframework.web.util.NestedServletException: Handler dispatch failed; nested exception is java.lang.NoClassDefFoundError: Could not initialize class com.dianping.cat.message.internal.DefaultMessageProducer
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1087)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.AbstractRequestLoggingFilter.doFilterInternal(AbstractRequestLoggingFilter.java:289)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.AbstractRequestLoggingFilter.doFilterInternal(AbstractRequestLoggingFilter.java:289)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.nrcp.trace.http.ServletFilter.doFilter(ServletFilter.java:63)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.openoa.common.config.XssFilter.doFilter(XssFilter.java:19)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.NoClassDefFoundError: Could not initialize class com.dianping.cat.message.internal.DefaultMessageProducer
	at com.dianping.cat.Cat.initializeInternal(Cat.java:311)
	at com.dianping.cat.Cat.checkAndInitialize(Cat.java:72)
	at com.dianping.cat.Cat.getProducer(Cat.java:211)
	at com.dianping.cat.Cat.newTransaction(Cat.java:627)
	at org.openoa.aspect.WebAspect.around(WebAspect.java:52)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at org.openoa.controller.UserController$$EnhancerBySpringCGLIB$$8ae4edad.queryByNameFuzzy(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	... 49 common frames omitted
2025-08-27 16:03:32.342 INFO  [pool-4-thread-1] com.nrcp.log.ErrorLogSender$1.run:100 - [traceId:] ErrorLogSender send error log [pre 环境antflow项目 TraceId: 00000000.1756281812169.********* ip: 127.0.0.1 Exception: 全局异常捕获: {}
org.springframework.web.util.NestedServletException: Handler dispatch failed; nested exception is java.lang.NoClassDefFoundError: Could not initialize class com.dianping.cat.message.internal.DefaultMessageProducer
at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1087)
at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
at org.springframework.web.filter.AbstractRequestLoggingFilter.doFilterInternal(AbstractRequestLoggingFilter.java:289)
at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
 argsList: ["Handler dispatch failed; nested exception is java.lang.NoClassDefFoundError: Could not initialize class com.dianping.cat.message.internal.DefaultMessageProducer"]]
2025-08-27 16:03:32.342 ERROR [http-nio-7001-exec-3] org.openoa.common.config.mvc.GlobalExceptionHandler.handler:133 - [traceId:00000000.1756281812169.*********] http://localhost:7001/user/queryByNameFuzzy
org.springframework.web.util.NestedServletException: Handler dispatch failed; nested exception is java.lang.NoClassDefFoundError: Could not initialize class com.dianping.cat.message.internal.DefaultMessageProducer
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1087)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.AbstractRequestLoggingFilter.doFilterInternal(AbstractRequestLoggingFilter.java:289)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.AbstractRequestLoggingFilter.doFilterInternal(AbstractRequestLoggingFilter.java:289)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.nrcp.trace.http.ServletFilter.doFilter(ServletFilter.java:63)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.openoa.common.config.XssFilter.doFilter(XssFilter.java:19)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.NoClassDefFoundError: Could not initialize class com.dianping.cat.message.internal.DefaultMessageProducer
	at com.dianping.cat.Cat.initializeInternal(Cat.java:311)
	at com.dianping.cat.Cat.checkAndInitialize(Cat.java:72)
	at com.dianping.cat.Cat.getProducer(Cat.java:211)
	at com.dianping.cat.Cat.newTransaction(Cat.java:627)
	at org.openoa.aspect.WebAspect.around(WebAspect.java:52)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at org.openoa.controller.UserController$$EnhancerBySpringCGLIB$$8ae4edad.queryByNameFuzzy(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	... 49 common frames omitted
2025-08-27 16:03:32.546 INFO  [pool-4-thread-1] com.nrcp.log.CompanyWeChatAlarmService.sendAlarm:50 - [traceId:] alarm call wechat roobat response info [{"errcode":0,"errmsg":"ok"}]
2025-08-27 16:03:32.547 INFO  [pool-4-thread-1] com.nrcp.log.ErrorLogSender$1.run:100 - [traceId:] ErrorLogSender send error log [pre 环境antflow项目 TraceId: 00000000.1756281812169.********* ip: 127.0.0.1 Exception: http://localhost:7001/user/queryByNameFuzzy
org.springframework.web.util.NestedServletException: Handler dispatch failed; nested exception is java.lang.NoClassDefFoundError: Could not initialize class com.dianping.cat.message.internal.DefaultMessageProducer
at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1087)
at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
at org.springframework.web.filter.AbstractRequestLoggingFilter.doFilterInternal(AbstractRequestLoggingFilter.java:289)
at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
 argsList: null]
2025-08-27 16:03:32.760 INFO  [pool-4-thread-1] com.nrcp.log.CompanyWeChatAlarmService.sendAlarm:50 - [traceId:] alarm call wechat roobat response info [{"errcode":0,"errmsg":"ok"}]
2025-08-27 16:04:20.723 INFO  [Thread-21] com.xxl.job.core.server.EmbedServer$1.run:96 - [traceId:] >>>>>>>>>>> xxl-job remoting server stop.
2025-08-27 16:04:20.822 INFO  [xxl-job, executor ExecutorRegistryThread] com.xxl.job.core.thread.ExecutorRegistryThread$1.run:87 - [traceId:] >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='antflow-pre', registryValue='http://********:9421/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-08-27 16:04:20.823 INFO  [xxl-job, executor ExecutorRegistryThread] com.xxl.job.core.thread.ExecutorRegistryThread$1.run:105 - [traceId:] >>>>>>>>>>> xxl-job, executor registry thread destory.
2025-08-27 16:04:20.823 INFO  [SpringApplicationShutdownHook] com.xxl.job.core.server.EmbedServer.stop:125 - [traceId:] >>>>>>>>>>> xxl-job remoting server destroy success.
2025-08-27 16:04:20.823 INFO  [xxl-job, executor JobLogFileCleanThread] com.xxl.job.core.thread.JobLogFileCleanThread$1.run:99 - [traceId:] >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2025-08-27 16:04:20.823 INFO  [xxl-job, executor TriggerCallbackThread] com.xxl.job.core.thread.TriggerCallbackThread$1.run:97 - [traceId:] >>>>>>>>>>> xxl-job, executor callback thread destory.
2025-08-27 16:04:20.823 INFO  [Thread-20] com.xxl.job.core.thread.TriggerCallbackThread$2.run:127 - [traceId:] >>>>>>>>>>> xxl-job, executor retry callback thread destory.
2025-08-27 16:10:57.356 INFO  [main] org.springframework.boot.StartupInfoLogger.logStarting:55 - [traceId:] Starting AntFlowApplication using Java 1.8.0_432 on KyleHsudeMacBook-Pro.local with PID 64131 (/Users/<USER>/shuma/source-code/Antflow/antflow-web/target/classes started by kylehsu in /Users/<USER>/shuma/source-code/Antflow)
2025-08-27 16:10:57.356 INFO  [main] org.springframework.boot.SpringApplication.logStartupProfileInfo:638 - [traceId:] The following 1 profile is active: "release"
2025-08-27 16:10:57.912 INFO  [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate.multipleStoresDetected:262 - [traceId:] Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-27 16:10:57.913 INFO  [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate.registerRepositoriesIn:132 - [traceId:] Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-27 16:10:58.006 INFO  [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate.registerRepositoriesIn:201 - [traceId:] Finished Spring Data repository scanning in 62 ms. Found 0 Redis repository interfaces.
2025-08-27 16:10:58.532 INFO  [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize:108 - [traceId:] Tomcat initialized with port(s): 8080 (http)
2025-08-27 16:10:58.536 INFO  [main] org.apache.juli.logging.DirectJDKLog.log:173 - [traceId:] Starting service [Tomcat]
2025-08-27 16:10:58.536 INFO  [main] org.apache.juli.logging.DirectJDKLog.log:173 - [traceId:] Starting Servlet engine: [Apache Tomcat/9.0.82]
2025-08-27 16:10:58.574 INFO  [main] org.apache.juli.logging.DirectJDKLog.log:173 - [traceId:] Initializing Spring embedded WebApplicationContext
2025-08-27 16:10:58.574 INFO  [main] org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.prepareWebApplicationContext:292 - [traceId:] Root WebApplicationContext: initialization completed in 1116 ms
2025-08-27 16:10:58.631 ERROR [main] org.springframework.boot.web.embedded.tomcat.TomcatStarter.onStartup:61 - [traceId:] Error starting Tomcat context. Exception: org.springframework.beans.factory.UnsatisfiedDependencyException. Message: Error creating bean with name 'jimuFilterRegistrationBean' defined in class path resource [org/openoa/engine/conf/mvc/MVCConf.class]: Unsatisfied dependency expressed through method 'logHandlerFilter' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'webRequestLoggingFilter': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'afUserService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'syncEhrOrgInfoServiceImpl': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'syncEhrOrgInfoMapper' defined in file [/Users/<USER>/shuma/source-code/Antflow/antflow-base/target/classes/org/openoa/base/mapper/SyncEhrOrgInfoMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Unsatisfied dependency expressed through method 'sqlSessionFactory' parameter 0; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'tenantAwareDataSource' defined in class path resource [org/openoa/engine/conf/engineconfig/TenantAwareDataSourceConfig.class]: Unsatisfied dependency expressed through method 'tenantAwareDataSource' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'multiTenantInfoHolder' defined in file [/Users/<USER>/shuma/source-code/Antflow/antflow-engine/target/classes/org/openoa/engine/conf/engineconfig/MultiTenantInfoHolder.class]: Invocation of init method failed; nested exception is org.springframework.boot.autoconfigure.jdbc.DataSourceProperties$DataSourceBeanCreationException: Failed to determine a suitable driver class
2025-08-27 16:10:58.641 INFO  [main] org.apache.juli.logging.DirectJDKLog.log:173 - [traceId:] Stopping service [Tomcat]
2025-08-27 16:10:58.646 WARN  [main] org.springframework.context.support.AbstractApplicationContext.refresh:591 - [traceId:] Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Unable to start web server; nested exception is org.springframework.boot.web.server.WebServerException: Unable to start embedded Tomcat
2025-08-27 16:10:58.651 INFO  [main] org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener.logMessage:136 - [traceId:] 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-08-27 16:10:58.660 ERROR [main] org.springframework.boot.diagnostics.LoggingFailureAnalysisReporter.report:40 - [traceId:] 

***************************
APPLICATION FAILED TO START
***************************

Description:

Failed to configure a DataSource: 'url' attribute is not specified and no embedded datasource could be configured.

Reason: Failed to determine a suitable driver class


Action:

Consider the following:
	If you want an embedded database (H2, HSQL or Derby), please put it on the classpath.
	If you have database settings to be loaded from a particular profile you may need to activate it (the profiles release are currently active).

2025-08-27 16:11:02.738 INFO  [main] org.springframework.boot.StartupInfoLogger.logStarting:55 - [traceId:] Starting AntFlowApplication using Java 1.8.0_432 on KyleHsudeMacBook-Pro.local with PID 64164 (/Users/<USER>/shuma/source-code/Antflow/antflow-web/target/classes started by kylehsu in /Users/<USER>/shuma/source-code/Antflow)
2025-08-27 16:11:02.739 INFO  [main] org.springframework.boot.SpringApplication.logStartupProfileInfo:638 - [traceId:] The following 1 profile is active: "pre"
2025-08-27 16:11:03.277 INFO  [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate.multipleStoresDetected:262 - [traceId:] Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-27 16:11:03.278 INFO  [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate.registerRepositoriesIn:132 - [traceId:] Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-27 16:11:03.366 INFO  [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate.registerRepositoriesIn:201 - [traceId:] Finished Spring Data repository scanning in 58 ms. Found 0 Redis repository interfaces.
2025-08-27 16:11:03.892 INFO  [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize:108 - [traceId:] Tomcat initialized with port(s): 7001 (http)
2025-08-27 16:11:03.896 INFO  [main] org.apache.juli.logging.DirectJDKLog.log:173 - [traceId:] Starting service [Tomcat]
2025-08-27 16:11:03.896 INFO  [main] org.apache.juli.logging.DirectJDKLog.log:173 - [traceId:] Starting Servlet engine: [Apache Tomcat/9.0.82]
2025-08-27 16:11:03.934 INFO  [main] org.apache.juli.logging.DirectJDKLog.log:173 - [traceId:] Initializing Spring embedded WebApplicationContext
2025-08-27 16:11:03.935 INFO  [main] org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.prepareWebApplicationContext:292 - [traceId:] Root WebApplicationContext: initialization completed in 1103 ms
2025-08-27 16:11:04.581 INFO  [main] com.zaxxer.hikari.HikariDataSource.getConnection:110 - [traceId:] HikariPool-1 - Starting...
2025-08-27 16:11:05.317 INFO  [main] com.zaxxer.hikari.HikariDataSource.getConnection:123 - [traceId:] HikariPool-1 - Start completed.
2025-08-27 16:11:05.570 INFO  [main] org.activiti.engine.impl.ProcessEngineImpl.<init>:86 - [traceId:] ProcessEngine default created
2025-08-27 16:11:05.572 INFO  [main] org.openoa.engine.bpmnconf.activitilistener.BpmnGlobalEventListener.onEvent:45 - [traceId:] AntFlow Engine is started
2025-08-27 16:11:05.572 INFO  [main] org.activiti.engine.impl.cfg.multitenant.MultiSchemaMultiTenantProcessEngineConfiguration.createTenantSchema:160 - [traceId:] creating/validating database schema for tenant 
2025-08-27 16:11:08.225 INFO  [main] org.openoa.base.dto.wecom.WechatConfigProperties.init:40 - [traceId:] ===> wechat config: WechatConfigProperties(zhongqi=WechatConfigProperties.WechatConfig(agentId=1000229, corpId=wwf1c477c862181a7f, corpsecret=IH5PGBOHD1dOMX86TOHi8UclM7uUUzaODKvbmE2m-ZA), chenxing=WechatConfigProperties.WechatConfig(agentId=1000099, corpId=wwe2351ed80f0479d4, corpsecret=_PhZTbZXs6-zucZq1efQXWW5L3ya69RUkTgQRj0eWQk), xinwang=WechatConfigProperties.WechatConfig(agentId=1000129, corpId=wwd674e2ffdc4f6862, corpsecret=kG1XfR6fwFoPBRBIPev92WVw5QtOu2UB0AiNx0TluG4), kuajing=WechatConfigProperties.WechatConfig(agentId=1000060, corpId=ww8bd429f5b618a57e, corpsecret=SFO9FKx4G0Yl_y5E8Jw5cf0DJHmuVisRsOQouayXYkM), jituan=WechatConfigProperties.WechatConfig(agentId=1000008, corpId=wwa0a360c6341c5ae6, corpsecret=GnyB1Y1-SWkutF8_vIDeyGj3WsYqzuDTo9eB5kzJbOg), smhg=WechatConfigProperties.WechatConfig(agentId=999999, corpId=999999, corpsecret=999999), xiaoxia=WechatConfigProperties.WechatConfig(agentId=1000011, corpId=ww71746e109858902c, corpsecret=jAYDpVHoRihTuXLJ8Zm1HPV5M1t9XMi7-861mGdl4rA))
2025-08-27 16:11:09.193 INFO  [main] cn.ce.xxljob.config.XxlAdminAddressConfig.afterPropertiesSet:91 - [traceId:] 处理配置项: xxl.job.admin.addresses:http://pre-omo.aiyouyi.cn/xxl-job-admin/
2025-08-27 16:11:09.194 INFO  [main] cn.ce.xxljob.config.XxlAdminAddressConfig.afterPropertiesSet:109 - [traceId:] Xxl Job Master Admin URL: http://pre-omo.aiyouyi.cn/xxl-job-admin/
2025-08-27 16:11:09.196 INFO  [main] cn.ce.xxljob.config.XxlJobConfig.xxlJobExecutor:46 - [traceId:] >>>>>>>>>>> xxl-job config init.
2025-08-27 16:11:09.523 INFO  [main] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:168 - [traceId:] >>>>>>>>>>> xxl-job register jobhandler success, name:ANTFLOW_SYNC_EHR_EMP_INFO_JOB_HANDLER, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@39159b14[class org.openoa.job.SyncEhrDataJob#syncEhrEmpInfoJobHandler]
2025-08-27 16:11:09.523 INFO  [main] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:168 - [traceId:] >>>>>>>>>>> xxl-job register jobhandler success, name:ANTFLOW_SYNC_EHR_ORG_INFO_JOB_HANDLER, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@57ab4b33[class org.openoa.job.SyncEhrDataJob#syncEhrOrgInfoJobHandler]
2025-08-27 16:11:09.614 INFO  [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer.start:220 - [traceId:] Tomcat started on port(s): 7001 (http) with context path ''
2025-08-27 16:11:09.635 INFO  [Thread-19] com.xxl.job.core.server.EmbedServer$1.run:86 - [traceId:] >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9421
2025-08-27 16:11:10.706 INFO  [main] org.springframework.boot.StartupInfoLogger.logStarted:61 - [traceId:] Started AntFlowApplication in 8.381 seconds (JVM running for 8.735)
2025-08-27 16:11:10.709 INFO  [main] org.openoa.common.config.StartedListener.onApplicationEvent:25 - [traceId:] [94myour project server started at: http://127.0.0.1:7001[0;39m
2025-08-27 16:11:10.709 INFO  [main] org.openoa.common.config.StartedListener.onApplicationEvent:27 - [traceId:] [93myour project server was started successfully on port:7001 with context path:/[0;39m
2025-08-27 16:11:10.709 ERROR [main] org.openoa.AntFlowApplication.main:15 - [traceId:] AntFlow 项目启动成功!
2025-08-27 16:11:22.729 INFO  [http-nio-7001-exec-1] org.apache.juli.logging.DirectJDKLog.log:173 - [traceId:] Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-27 16:11:22.729 INFO  [http-nio-7001-exec-1] org.springframework.web.servlet.FrameworkServlet.initServletBean:525 - [traceId:] Initializing Servlet 'dispatcherServlet'
2025-08-27 16:11:22.730 INFO  [http-nio-7001-exec-1] org.springframework.web.servlet.FrameworkServlet.initServletBean:547 - [traceId:] Completed initialization in 1 ms
2025-08-27 16:11:22.977 INFO  [http-nio-7001-exec-1] org.openoa.aspect.AuthInterceptor.preHandle:25 - [traceId:00000000.1756282282746.641641000] 员工=侯晓瑶, jt83326, 请求路径：/user/queryByNameFuzzy
2025-08-27 16:11:24.194 ERROR [http-nio-7001-exec-1] org.openoa.common.config.mvc.GlobalExceptionHandler.handler:43 - [traceId:00000000.1756282282746.641641000] 全局异常捕获: Handler dispatch failed; nested exception is java.lang.ExceptionInInitializerError
org.springframework.web.util.NestedServletException: Handler dispatch failed; nested exception is java.lang.ExceptionInInitializerError
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1087)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.AbstractRequestLoggingFilter.doFilterInternal(AbstractRequestLoggingFilter.java:289)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.AbstractRequestLoggingFilter.doFilterInternal(AbstractRequestLoggingFilter.java:289)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.nrcp.trace.http.ServletFilter.doFilter(ServletFilter.java:63)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.openoa.common.config.XssFilter.doFilter(XssFilter.java:19)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.ExceptionInInitializerError: null
	at com.dianping.cat.message.internal.DefaultMessageManager.<init>(DefaultMessageManager.java:45)
	at com.dianping.cat.message.internal.DefaultMessageManager.<clinit>(DefaultMessageManager.java:52)
	at com.dianping.cat.message.internal.DefaultMessageProducer.<init>(DefaultMessageProducer.java:33)
	at com.dianping.cat.message.internal.DefaultMessageProducer.<clinit>(DefaultMessageProducer.java:37)
	at com.dianping.cat.Cat.initializeInternal(Cat.java:311)
	at com.dianping.cat.Cat.checkAndInitialize(Cat.java:72)
	at com.dianping.cat.Cat.getProducer(Cat.java:211)
	at com.dianping.cat.Cat.newTransaction(Cat.java:627)
	at org.openoa.aspect.WebAspect.around(WebAspect.java:52)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at org.openoa.controller.UserController$$EnhancerBySpringCGLIB$$482ba42d.queryByNameFuzzy(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	... 49 common frames omitted
Caused by: java.lang.RuntimeException: Error when get cat router service, please contact cat support team for help!
	at com.dianping.cat.configuration.ApplicationEnvironment.loadClientConfig(ApplicationEnvironment.java:151)
	at com.dianping.cat.configuration.DefaultClientConfigService.<init>(DefaultClientConfigService.java:72)
	at com.dianping.cat.configuration.DefaultClientConfigService.<clinit>(DefaultClientConfigService.java:52)
	... 83 common frames omitted
Caused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://org.cat/cat/s/launch?ip=**************&env=unknown&hostname=KyleHsudeMacBook-Pro.local
	at sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1932)
	at sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1520)
	at com.dianping.cat.util.Urls$UrlIO.openStream(Urls.java:83)
	at com.dianping.cat.util.Urls$UrlIO.openStream(Urls.java:59)
	at com.dianping.cat.util.NetworkHelper.readFromUrlWithRetry(NetworkHelper.java:36)
	at com.dianping.cat.configuration.ApplicationEnvironment.loadRemoteClientConfig(ApplicationEnvironment.java:168)
	at com.dianping.cat.configuration.ApplicationEnvironment.loadClientConfig(ApplicationEnvironment.java:132)
	... 85 common frames omitted
2025-08-27 16:11:24.197 ERROR [http-nio-7001-exec-1] org.openoa.common.config.mvc.GlobalExceptionHandler.handler:133 - [traceId:00000000.1756282282746.641641000] http://localhost:7001/user/queryByNameFuzzy
org.springframework.web.util.NestedServletException: Handler dispatch failed; nested exception is java.lang.ExceptionInInitializerError
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1087)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.AbstractRequestLoggingFilter.doFilterInternal(AbstractRequestLoggingFilter.java:289)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.AbstractRequestLoggingFilter.doFilterInternal(AbstractRequestLoggingFilter.java:289)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.nrcp.trace.http.ServletFilter.doFilter(ServletFilter.java:63)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.openoa.common.config.XssFilter.doFilter(XssFilter.java:19)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.ExceptionInInitializerError: null
	at com.dianping.cat.message.internal.DefaultMessageManager.<init>(DefaultMessageManager.java:45)
	at com.dianping.cat.message.internal.DefaultMessageManager.<clinit>(DefaultMessageManager.java:52)
	at com.dianping.cat.message.internal.DefaultMessageProducer.<init>(DefaultMessageProducer.java:33)
	at com.dianping.cat.message.internal.DefaultMessageProducer.<clinit>(DefaultMessageProducer.java:37)
	at com.dianping.cat.Cat.initializeInternal(Cat.java:311)
	at com.dianping.cat.Cat.checkAndInitialize(Cat.java:72)
	at com.dianping.cat.Cat.getProducer(Cat.java:211)
	at com.dianping.cat.Cat.newTransaction(Cat.java:627)
	at org.openoa.aspect.WebAspect.around(WebAspect.java:52)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at org.openoa.controller.UserController$$EnhancerBySpringCGLIB$$482ba42d.queryByNameFuzzy(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	... 49 common frames omitted
Caused by: java.lang.RuntimeException: Error when get cat router service, please contact cat support team for help!
	at com.dianping.cat.configuration.ApplicationEnvironment.loadClientConfig(ApplicationEnvironment.java:151)
	at com.dianping.cat.configuration.DefaultClientConfigService.<init>(DefaultClientConfigService.java:72)
	at com.dianping.cat.configuration.DefaultClientConfigService.<clinit>(DefaultClientConfigService.java:52)
	... 83 common frames omitted
Caused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://org.cat/cat/s/launch?ip=**************&env=unknown&hostname=KyleHsudeMacBook-Pro.local
	at sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1932)
	at sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1520)
	at com.dianping.cat.util.Urls$UrlIO.openStream(Urls.java:83)
	at com.dianping.cat.util.Urls$UrlIO.openStream(Urls.java:59)
	at com.dianping.cat.util.NetworkHelper.readFromUrlWithRetry(NetworkHelper.java:36)
	at com.dianping.cat.configuration.ApplicationEnvironment.loadRemoteClientConfig(ApplicationEnvironment.java:168)
	at com.dianping.cat.configuration.ApplicationEnvironment.loadClientConfig(ApplicationEnvironment.java:132)
	... 85 common frames omitted
2025-08-27 16:13:33.723 INFO  [main] org.springframework.boot.StartupInfoLogger.logStarting:55 - [traceId:] Starting AntFlowApplication using Java 1.8.0_432 on KyleHsudeMacBook-Pro.local with PID 64558 (/Users/<USER>/shuma/source-code/Antflow/antflow-web/target/classes started by kylehsu in /Users/<USER>/shuma/source-code/Antflow)
2025-08-27 16:13:33.724 INFO  [main] org.springframework.boot.SpringApplication.logStartupProfileInfo:638 - [traceId:] The following 1 profile is active: "pre"
2025-08-27 16:13:34.387 INFO  [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate.multipleStoresDetected:262 - [traceId:] Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-27 16:13:34.388 INFO  [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate.registerRepositoriesIn:132 - [traceId:] Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-27 16:13:34.417 INFO  [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate.registerRepositoriesIn:201 - [traceId:] Finished Spring Data repository scanning in 21 ms. Found 0 Redis repository interfaces.
2025-08-27 16:13:35.042 INFO  [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize:108 - [traceId:] Tomcat initialized with port(s): 7001 (http)
2025-08-27 16:13:35.047 INFO  [main] org.apache.juli.logging.DirectJDKLog.log:173 - [traceId:] Starting service [Tomcat]
2025-08-27 16:13:35.048 INFO  [main] org.apache.juli.logging.DirectJDKLog.log:173 - [traceId:] Starting Servlet engine: [Apache Tomcat/9.0.82]
2025-08-27 16:13:35.106 INFO  [main] org.apache.juli.logging.DirectJDKLog.log:173 - [traceId:] Initializing Spring embedded WebApplicationContext
2025-08-27 16:13:35.107 INFO  [main] org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.prepareWebApplicationContext:292 - [traceId:] Root WebApplicationContext: initialization completed in 1245 ms
2025-08-27 16:13:36.003 INFO  [main] com.zaxxer.hikari.HikariDataSource.getConnection:110 - [traceId:] HikariPool-1 - Starting...
2025-08-27 16:13:37.198 INFO  [main] com.zaxxer.hikari.HikariDataSource.getConnection:123 - [traceId:] HikariPool-1 - Start completed.
2025-08-27 16:13:37.420 INFO  [main] org.activiti.engine.impl.ProcessEngineImpl.<init>:86 - [traceId:] ProcessEngine default created
2025-08-27 16:13:37.426 INFO  [main] org.openoa.engine.bpmnconf.activitilistener.BpmnGlobalEventListener.onEvent:45 - [traceId:] AntFlow Engine is started
2025-08-27 16:13:37.426 INFO  [main] org.activiti.engine.impl.cfg.multitenant.MultiSchemaMultiTenantProcessEngineConfiguration.createTenantSchema:160 - [traceId:] creating/validating database schema for tenant 
2025-08-27 16:13:43.320 INFO  [main] org.openoa.base.dto.wecom.WechatConfigProperties.init:40 - [traceId:] ===> wechat config: WechatConfigProperties(zhongqi=WechatConfigProperties.WechatConfig(agentId=1000229, corpId=wwf1c477c862181a7f, corpsecret=IH5PGBOHD1dOMX86TOHi8UclM7uUUzaODKvbmE2m-ZA), chenxing=WechatConfigProperties.WechatConfig(agentId=1000099, corpId=wwe2351ed80f0479d4, corpsecret=_PhZTbZXs6-zucZq1efQXWW5L3ya69RUkTgQRj0eWQk), xinwang=WechatConfigProperties.WechatConfig(agentId=1000129, corpId=wwd674e2ffdc4f6862, corpsecret=kG1XfR6fwFoPBRBIPev92WVw5QtOu2UB0AiNx0TluG4), kuajing=WechatConfigProperties.WechatConfig(agentId=1000060, corpId=ww8bd429f5b618a57e, corpsecret=SFO9FKx4G0Yl_y5E8Jw5cf0DJHmuVisRsOQouayXYkM), jituan=WechatConfigProperties.WechatConfig(agentId=1000008, corpId=wwa0a360c6341c5ae6, corpsecret=GnyB1Y1-SWkutF8_vIDeyGj3WsYqzuDTo9eB5kzJbOg), smhg=WechatConfigProperties.WechatConfig(agentId=999999, corpId=999999, corpsecret=999999), xiaoxia=WechatConfigProperties.WechatConfig(agentId=1000011, corpId=ww71746e109858902c, corpsecret=jAYDpVHoRihTuXLJ8Zm1HPV5M1t9XMi7-861mGdl4rA))
2025-08-27 16:13:44.358 INFO  [main] cn.ce.xxljob.config.XxlAdminAddressConfig.afterPropertiesSet:91 - [traceId:] 处理配置项: xxl.job.admin.addresses:http://pre-omo.aiyouyi.cn/xxl-job-admin/
2025-08-27 16:13:44.358 INFO  [main] cn.ce.xxljob.config.XxlAdminAddressConfig.afterPropertiesSet:109 - [traceId:] Xxl Job Master Admin URL: http://pre-omo.aiyouyi.cn/xxl-job-admin/
2025-08-27 16:13:44.362 INFO  [main] cn.ce.xxljob.config.XxlJobConfig.xxlJobExecutor:46 - [traceId:] >>>>>>>>>>> xxl-job config init.
2025-08-27 16:13:44.619 INFO  [main] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:168 - [traceId:] >>>>>>>>>>> xxl-job register jobhandler success, name:ANTFLOW_SYNC_EHR_EMP_INFO_JOB_HANDLER, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@68c47cf9[class org.openoa.job.SyncEhrDataJob#syncEhrEmpInfoJobHandler]
2025-08-27 16:13:44.619 INFO  [main] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:168 - [traceId:] >>>>>>>>>>> xxl-job register jobhandler success, name:ANTFLOW_SYNC_EHR_ORG_INFO_JOB_HANDLER, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@ecb8b3e[class org.openoa.job.SyncEhrDataJob#syncEhrOrgInfoJobHandler]
2025-08-27 16:13:44.696 INFO  [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer.start:220 - [traceId:] Tomcat started on port(s): 7001 (http) with context path ''
2025-08-27 16:13:44.705 INFO  [main] org.springframework.boot.StartupInfoLogger.logStarted:61 - [traceId:] Started AntFlowApplication in 11.283 seconds (JVM running for 11.618)
2025-08-27 16:13:44.707 INFO  [main] org.openoa.common.config.StartedListener.onApplicationEvent:25 - [traceId:] your project server started at: http://127.0.0.1:7001
2025-08-27 16:13:44.707 INFO  [main] org.openoa.common.config.StartedListener.onApplicationEvent:27 - [traceId:] your project server was started successfully on port:7001 with context path:/
2025-08-27 16:13:44.707 ERROR [main] org.openoa.AntFlowApplication.main:15 - [traceId:] AntFlow 项目启动成功!
2025-08-27 16:13:44.727 INFO  [Thread-6] com.xxl.job.core.server.EmbedServer$1.run:86 - [traceId:] >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9421
2025-08-27 16:13:51.302 INFO  [http-nio-7001-exec-1] org.apache.juli.logging.DirectJDKLog.log:173 - [traceId:] Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-27 16:13:51.302 INFO  [http-nio-7001-exec-1] org.springframework.web.servlet.FrameworkServlet.initServletBean:525 - [traceId:] Initializing Servlet 'dispatcherServlet'
2025-08-27 16:13:51.303 INFO  [http-nio-7001-exec-1] org.springframework.web.servlet.FrameworkServlet.initServletBean:547 - [traceId:] Completed initialization in 0 ms
2025-08-27 16:13:51.953 INFO  [http-nio-7001-exec-1] org.openoa.aspect.AuthInterceptor.preHandle:25 - [traceId:00000000.1756282431324.645581000] 员工=侯晓瑶, jt83326, 请求路径：/user/queryByNameFuzzy
2025-08-27 16:13:54.620 ERROR [http-nio-7001-exec-1] org.openoa.common.config.mvc.GlobalExceptionHandler.handler:43 - [traceId:00000000.1756282431324.645581000] 全局异常捕获: Handler dispatch failed; nested exception is java.lang.ExceptionInInitializerError
org.springframework.web.util.NestedServletException: Handler dispatch failed; nested exception is java.lang.ExceptionInInitializerError
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1087)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.AbstractRequestLoggingFilter.doFilterInternal(AbstractRequestLoggingFilter.java:289)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.AbstractRequestLoggingFilter.doFilterInternal(AbstractRequestLoggingFilter.java:289)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.nrcp.trace.http.ServletFilter.doFilter(ServletFilter.java:63)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.openoa.common.config.XssFilter.doFilter(XssFilter.java:19)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.ExceptionInInitializerError: null
	at com.dianping.cat.message.internal.DefaultMessageManager.<init>(DefaultMessageManager.java:45)
	at com.dianping.cat.message.internal.DefaultMessageManager.<clinit>(DefaultMessageManager.java:52)
	at com.dianping.cat.message.internal.DefaultMessageProducer.<init>(DefaultMessageProducer.java:33)
	at com.dianping.cat.message.internal.DefaultMessageProducer.<clinit>(DefaultMessageProducer.java:37)
	at com.dianping.cat.Cat.initializeInternal(Cat.java:311)
	at com.dianping.cat.Cat.checkAndInitialize(Cat.java:72)
	at com.dianping.cat.Cat.getProducer(Cat.java:211)
	at com.dianping.cat.Cat.newTransaction(Cat.java:627)
	at org.openoa.aspect.WebAspect.around(WebAspect.java:52)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at org.openoa.controller.UserController$$EnhancerBySpringCGLIB$$d5cec3d2.queryByNameFuzzy(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	... 49 common frames omitted
Caused by: java.lang.RuntimeException: Error when get cat router service, please contact cat support team for help!
	at com.dianping.cat.configuration.ApplicationEnvironment.loadClientConfig(ApplicationEnvironment.java:151)
	at com.dianping.cat.configuration.DefaultClientConfigService.<init>(DefaultClientConfigService.java:72)
	at com.dianping.cat.configuration.DefaultClientConfigService.<clinit>(DefaultClientConfigService.java:52)
	... 83 common frames omitted
Caused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://org.cat/cat/s/launch?ip=**************&env=unknown&hostname=KyleHsudeMacBook-Pro.local
	at sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1932)
	at sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1520)
	at com.dianping.cat.util.Urls$UrlIO.openStream(Urls.java:83)
	at com.dianping.cat.util.Urls$UrlIO.openStream(Urls.java:59)
	at com.dianping.cat.util.NetworkHelper.readFromUrlWithRetry(NetworkHelper.java:36)
	at com.dianping.cat.configuration.ApplicationEnvironment.loadRemoteClientConfig(ApplicationEnvironment.java:168)
	at com.dianping.cat.configuration.ApplicationEnvironment.loadClientConfig(ApplicationEnvironment.java:132)
	... 85 common frames omitted
2025-08-27 16:13:54.621 ERROR [http-nio-7001-exec-1] org.openoa.common.config.mvc.GlobalExceptionHandler.handler:133 - [traceId:00000000.1756282431324.645581000] http://localhost:7001/user/queryByNameFuzzy
org.springframework.web.util.NestedServletException: Handler dispatch failed; nested exception is java.lang.ExceptionInInitializerError
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1087)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.AbstractRequestLoggingFilter.doFilterInternal(AbstractRequestLoggingFilter.java:289)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.AbstractRequestLoggingFilter.doFilterInternal(AbstractRequestLoggingFilter.java:289)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.nrcp.trace.http.ServletFilter.doFilter(ServletFilter.java:63)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.openoa.common.config.XssFilter.doFilter(XssFilter.java:19)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.ExceptionInInitializerError: null
	at com.dianping.cat.message.internal.DefaultMessageManager.<init>(DefaultMessageManager.java:45)
	at com.dianping.cat.message.internal.DefaultMessageManager.<clinit>(DefaultMessageManager.java:52)
	at com.dianping.cat.message.internal.DefaultMessageProducer.<init>(DefaultMessageProducer.java:33)
	at com.dianping.cat.message.internal.DefaultMessageProducer.<clinit>(DefaultMessageProducer.java:37)
	at com.dianping.cat.Cat.initializeInternal(Cat.java:311)
	at com.dianping.cat.Cat.checkAndInitialize(Cat.java:72)
	at com.dianping.cat.Cat.getProducer(Cat.java:211)
	at com.dianping.cat.Cat.newTransaction(Cat.java:627)
	at org.openoa.aspect.WebAspect.around(WebAspect.java:52)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at org.openoa.controller.UserController$$EnhancerBySpringCGLIB$$d5cec3d2.queryByNameFuzzy(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	... 49 common frames omitted
Caused by: java.lang.RuntimeException: Error when get cat router service, please contact cat support team for help!
	at com.dianping.cat.configuration.ApplicationEnvironment.loadClientConfig(ApplicationEnvironment.java:151)
	at com.dianping.cat.configuration.DefaultClientConfigService.<init>(DefaultClientConfigService.java:72)
	at com.dianping.cat.configuration.DefaultClientConfigService.<clinit>(DefaultClientConfigService.java:52)
	... 83 common frames omitted
Caused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://org.cat/cat/s/launch?ip=**************&env=unknown&hostname=KyleHsudeMacBook-Pro.local
	at sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1932)
	at sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1520)
	at com.dianping.cat.util.Urls$UrlIO.openStream(Urls.java:83)
	at com.dianping.cat.util.Urls$UrlIO.openStream(Urls.java:59)
	at com.dianping.cat.util.NetworkHelper.readFromUrlWithRetry(NetworkHelper.java:36)
	at com.dianping.cat.configuration.ApplicationEnvironment.loadRemoteClientConfig(ApplicationEnvironment.java:168)
	at com.dianping.cat.configuration.ApplicationEnvironment.loadClientConfig(ApplicationEnvironment.java:132)
	... 85 common frames omitted
2025-08-27 16:14:52.842 INFO  [Thread-6] com.xxl.job.core.server.EmbedServer$1.run:96 - [traceId:] >>>>>>>>>>> xxl-job remoting server stop.
2025-08-27 16:14:52.945 INFO  [xxl-job, executor ExecutorRegistryThread] com.xxl.job.core.thread.ExecutorRegistryThread$1.run:87 - [traceId:] >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='antflow-pre', registryValue='http://********:9421/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-08-27 16:14:52.946 INFO  [xxl-job, executor ExecutorRegistryThread] com.xxl.job.core.thread.ExecutorRegistryThread$1.run:105 - [traceId:] >>>>>>>>>>> xxl-job, executor registry thread destory.
2025-08-27 16:14:52.946 INFO  [SpringApplicationShutdownHook] com.xxl.job.core.server.EmbedServer.stop:125 - [traceId:] >>>>>>>>>>> xxl-job remoting server destroy success.
2025-08-27 16:14:52.947 INFO  [xxl-job, executor JobLogFileCleanThread] com.xxl.job.core.thread.JobLogFileCleanThread$1.run:99 - [traceId:] >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2025-08-27 16:14:52.948 INFO  [xxl-job, executor TriggerCallbackThread] com.xxl.job.core.thread.TriggerCallbackThread$1.run:97 - [traceId:] >>>>>>>>>>> xxl-job, executor callback thread destory.
2025-08-27 16:14:52.949 INFO  [Thread-5] com.xxl.job.core.thread.TriggerCallbackThread$2.run:127 - [traceId:] >>>>>>>>>>> xxl-job, executor retry callback thread destory.
2025-08-27 16:14:55.107 INFO  [main] org.springframework.boot.StartupInfoLogger.logStarting:55 - [traceId:] Starting AntFlowApplication using Java 1.8.0_432 on KyleHsudeMacBook-Pro.local with PID 64763 (/Users/<USER>/shuma/source-code/Antflow/antflow-web/target/classes started by kylehsu in /Users/<USER>/shuma/source-code/Antflow)
2025-08-27 16:14:55.108 INFO  [main] org.springframework.boot.SpringApplication.logStartupProfileInfo:638 - [traceId:] The following 1 profile is active: "pre"
2025-08-27 16:14:55.760 INFO  [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate.multipleStoresDetected:262 - [traceId:] Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-27 16:14:55.762 INFO  [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate.registerRepositoriesIn:132 - [traceId:] Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-27 16:14:55.789 INFO  [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate.registerRepositoriesIn:201 - [traceId:] Finished Spring Data repository scanning in 19 ms. Found 0 Redis repository interfaces.
2025-08-27 16:14:56.402 INFO  [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize:108 - [traceId:] Tomcat initialized with port(s): 7001 (http)
2025-08-27 16:14:56.406 INFO  [main] org.apache.juli.logging.DirectJDKLog.log:173 - [traceId:] Starting service [Tomcat]
2025-08-27 16:14:56.407 INFO  [main] org.apache.juli.logging.DirectJDKLog.log:173 - [traceId:] Starting Servlet engine: [Apache Tomcat/9.0.82]
2025-08-27 16:14:56.460 INFO  [main] org.apache.juli.logging.DirectJDKLog.log:173 - [traceId:] Initializing Spring embedded WebApplicationContext
2025-08-27 16:14:56.460 INFO  [main] org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.prepareWebApplicationContext:292 - [traceId:] Root WebApplicationContext: initialization completed in 1221 ms
2025-08-27 16:14:57.340 INFO  [main] com.zaxxer.hikari.HikariDataSource.getConnection:110 - [traceId:] HikariPool-1 - Starting...
2025-08-27 16:14:58.126 INFO  [main] com.zaxxer.hikari.HikariDataSource.getConnection:123 - [traceId:] HikariPool-1 - Start completed.
2025-08-27 16:14:58.331 INFO  [main] org.activiti.engine.impl.ProcessEngineImpl.<init>:86 - [traceId:] ProcessEngine default created
2025-08-27 16:14:58.334 INFO  [main] org.openoa.engine.bpmnconf.activitilistener.BpmnGlobalEventListener.onEvent:45 - [traceId:] AntFlow Engine is started
2025-08-27 16:14:58.334 INFO  [main] org.activiti.engine.impl.cfg.multitenant.MultiSchemaMultiTenantProcessEngineConfiguration.createTenantSchema:160 - [traceId:] creating/validating database schema for tenant 
2025-08-27 16:15:00.623 INFO  [main] org.openoa.base.dto.wecom.WechatConfigProperties.init:40 - [traceId:] ===> wechat config: WechatConfigProperties(zhongqi=WechatConfigProperties.WechatConfig(agentId=1000229, corpId=wwf1c477c862181a7f, corpsecret=IH5PGBOHD1dOMX86TOHi8UclM7uUUzaODKvbmE2m-ZA), chenxing=WechatConfigProperties.WechatConfig(agentId=1000099, corpId=wwe2351ed80f0479d4, corpsecret=_PhZTbZXs6-zucZq1efQXWW5L3ya69RUkTgQRj0eWQk), xinwang=WechatConfigProperties.WechatConfig(agentId=1000129, corpId=wwd674e2ffdc4f6862, corpsecret=kG1XfR6fwFoPBRBIPev92WVw5QtOu2UB0AiNx0TluG4), kuajing=WechatConfigProperties.WechatConfig(agentId=1000060, corpId=ww8bd429f5b618a57e, corpsecret=SFO9FKx4G0Yl_y5E8Jw5cf0DJHmuVisRsOQouayXYkM), jituan=WechatConfigProperties.WechatConfig(agentId=1000008, corpId=wwa0a360c6341c5ae6, corpsecret=GnyB1Y1-SWkutF8_vIDeyGj3WsYqzuDTo9eB5kzJbOg), smhg=WechatConfigProperties.WechatConfig(agentId=999999, corpId=999999, corpsecret=999999), xiaoxia=WechatConfigProperties.WechatConfig(agentId=1000011, corpId=ww71746e109858902c, corpsecret=jAYDpVHoRihTuXLJ8Zm1HPV5M1t9XMi7-861mGdl4rA))
2025-08-27 16:15:01.565 INFO  [main] cn.ce.xxljob.config.XxlAdminAddressConfig.afterPropertiesSet:91 - [traceId:] 处理配置项: xxl.job.admin.addresses:http://pre-omo.aiyouyi.cn/xxl-job-admin/
2025-08-27 16:15:01.565 INFO  [main] cn.ce.xxljob.config.XxlAdminAddressConfig.afterPropertiesSet:109 - [traceId:] Xxl Job Master Admin URL: http://pre-omo.aiyouyi.cn/xxl-job-admin/
2025-08-27 16:15:01.568 INFO  [main] cn.ce.xxljob.config.XxlJobConfig.xxlJobExecutor:46 - [traceId:] >>>>>>>>>>> xxl-job config init.
2025-08-27 16:15:01.825 INFO  [main] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:168 - [traceId:] >>>>>>>>>>> xxl-job register jobhandler success, name:ANTFLOW_SYNC_EHR_EMP_INFO_JOB_HANDLER, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@47c422d3[class org.openoa.job.SyncEhrDataJob#syncEhrEmpInfoJobHandler]
2025-08-27 16:15:01.825 INFO  [main] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:168 - [traceId:] >>>>>>>>>>> xxl-job register jobhandler success, name:ANTFLOW_SYNC_EHR_ORG_INFO_JOB_HANDLER, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@382608d0[class org.openoa.job.SyncEhrDataJob#syncEhrOrgInfoJobHandler]
2025-08-27 16:15:01.904 INFO  [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer.start:220 - [traceId:] Tomcat started on port(s): 7001 (http) with context path ''
2025-08-27 16:15:01.912 INFO  [main] org.springframework.boot.StartupInfoLogger.logStarted:61 - [traceId:] Started AntFlowApplication in 7.104 seconds (JVM running for 7.42)
2025-08-27 16:15:01.914 INFO  [main] org.openoa.common.config.StartedListener.onApplicationEvent:25 - [traceId:] your project server started at: http://127.0.0.1:7001
2025-08-27 16:15:01.914 INFO  [main] org.openoa.common.config.StartedListener.onApplicationEvent:27 - [traceId:] your project server was started successfully on port:7001 with context path:/
2025-08-27 16:15:01.914 ERROR [main] org.openoa.AntFlowApplication.main:15 - [traceId:] AntFlow 项目启动成功!
2025-08-27 16:15:01.934 INFO  [Thread-6] com.xxl.job.core.server.EmbedServer$1.run:86 - [traceId:] >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9421
2025-08-27 16:15:20.896 INFO  [http-nio-7001-exec-1] org.apache.juli.logging.DirectJDKLog.log:173 - [traceId:] Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-27 16:15:20.897 INFO  [http-nio-7001-exec-1] org.springframework.web.servlet.FrameworkServlet.initServletBean:525 - [traceId:] Initializing Servlet 'dispatcherServlet'
2025-08-27 16:15:20.897 INFO  [http-nio-7001-exec-1] org.springframework.web.servlet.FrameworkServlet.initServletBean:547 - [traceId:] Completed initialization in 0 ms
2025-08-27 16:15:21.252 INFO  [http-nio-7001-exec-1] org.openoa.aspect.AuthInterceptor.preHandle:25 - [traceId:00000000.1756282520917.647631000] 员工=侯晓瑶, jt83326, 请求路径：/user/queryByNameFuzzy
2025-08-27 16:16:31.202 INFO  [http-nio-7001-exec-2] org.openoa.aspect.AuthInterceptor.preHandle:25 - [traceId:00000000.1756282591002.647631001] 员工=侯晓瑶, jt83326, 请求路径：/user/queryByNameFuzzy
2025-08-27 16:19:21.900 WARN  [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool$HouseKeeper.run:788 - [traceId:] HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=2m36s700ms).
2025-08-27 16:23:22.413 INFO  [http-nio-7001-exec-4] org.openoa.aspect.AuthInterceptor.preHandle:25 - [traceId:00000000.1756283002289.647631002] 员工=侯晓瑶, jt83326, 请求路径：/user/queryByNameFuzzy
2025-08-27 16:24:43.762 WARN  [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool$HouseKeeper.run:788 - [traceId:] HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=48s39ms).
2025-08-27 16:25:35.971 WARN  [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool$HouseKeeper.run:788 - [traceId:] HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=52s209ms).
2025-08-27 16:25:45.248 INFO  [http-nio-7001-exec-5] org.openoa.aspect.AuthInterceptor.preHandle:25 - [traceId:00000000.1756283145076.647631003] 员工=侯晓瑶, jt83326, 请求路径：/user/queryByNameFuzzy
2025-08-27 16:26:50.286 INFO  [Thread-6] com.xxl.job.core.server.EmbedServer$1.run:96 - [traceId:] >>>>>>>>>>> xxl-job remoting server stop.
2025-08-27 16:26:50.383 INFO  [xxl-job, executor ExecutorRegistryThread] com.xxl.job.core.thread.ExecutorRegistryThread$1.run:87 - [traceId:] >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='antflow-pre', registryValue='http://********:9421/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-08-27 16:26:50.383 INFO  [xxl-job, executor ExecutorRegistryThread] com.xxl.job.core.thread.ExecutorRegistryThread$1.run:105 - [traceId:] >>>>>>>>>>> xxl-job, executor registry thread destory.
2025-08-27 16:26:50.383 INFO  [SpringApplicationShutdownHook] com.xxl.job.core.server.EmbedServer.stop:125 - [traceId:] >>>>>>>>>>> xxl-job remoting server destroy success.
2025-08-27 16:26:50.383 INFO  [xxl-job, executor JobLogFileCleanThread] com.xxl.job.core.thread.JobLogFileCleanThread$1.run:99 - [traceId:] >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2025-08-27 16:26:50.383 INFO  [xxl-job, executor TriggerCallbackThread] com.xxl.job.core.thread.TriggerCallbackThread$1.run:97 - [traceId:] >>>>>>>>>>> xxl-job, executor callback thread destory.
2025-08-27 16:26:50.384 INFO  [Thread-5] com.xxl.job.core.thread.TriggerCallbackThread$2.run:127 - [traceId:] >>>>>>>>>>> xxl-job, executor retry callback thread destory.
2025-08-27 16:26:52.014 INFO  [main] org.springframework.boot.StartupInfoLogger.logStarting:55 - [traceId:] Starting AntFlowApplication using Java 1.8.0_432 on KyleHsudeMacBook-Pro.local with PID 66491 (/Users/<USER>/shuma/source-code/Antflow/antflow-web/target/classes started by kylehsu in /Users/<USER>/shuma/source-code/Antflow)
2025-08-27 16:26:52.015 INFO  [main] org.springframework.boot.SpringApplication.logStartupProfileInfo:638 - [traceId:] The following 1 profile is active: "pre"
2025-08-27 16:26:52.694 INFO  [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate.multipleStoresDetected:262 - [traceId:] Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-27 16:26:52.695 INFO  [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate.registerRepositoriesIn:132 - [traceId:] Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-27 16:26:52.725 INFO  [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate.registerRepositoriesIn:201 - [traceId:] Finished Spring Data repository scanning in 22 ms. Found 0 Redis repository interfaces.
2025-08-27 16:26:53.365 INFO  [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize:108 - [traceId:] Tomcat initialized with port(s): 7001 (http)
2025-08-27 16:26:53.370 INFO  [main] org.apache.juli.logging.DirectJDKLog.log:173 - [traceId:] Starting service [Tomcat]
2025-08-27 16:26:53.370 INFO  [main] org.apache.juli.logging.DirectJDKLog.log:173 - [traceId:] Starting Servlet engine: [Apache Tomcat/9.0.82]
2025-08-27 16:26:53.425 INFO  [main] org.apache.juli.logging.DirectJDKLog.log:173 - [traceId:] Initializing Spring embedded WebApplicationContext
2025-08-27 16:26:53.425 INFO  [main] org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.prepareWebApplicationContext:292 - [traceId:] Root WebApplicationContext: initialization completed in 1278 ms
2025-08-27 16:26:54.333 INFO  [main] com.zaxxer.hikari.HikariDataSource.getConnection:110 - [traceId:] HikariPool-1 - Starting...
2025-08-27 16:26:55.129 INFO  [main] com.zaxxer.hikari.HikariDataSource.getConnection:123 - [traceId:] HikariPool-1 - Start completed.
2025-08-27 16:26:55.339 INFO  [main] org.activiti.engine.impl.ProcessEngineImpl.<init>:86 - [traceId:] ProcessEngine default created
2025-08-27 16:26:55.345 INFO  [main] org.openoa.engine.bpmnconf.activitilistener.BpmnGlobalEventListener.onEvent:45 - [traceId:] AntFlow Engine is started
2025-08-27 16:26:55.345 INFO  [main] org.activiti.engine.impl.cfg.multitenant.MultiSchemaMultiTenantProcessEngineConfiguration.createTenantSchema:160 - [traceId:] creating/validating database schema for tenant 
2025-08-27 16:26:58.484 INFO  [main] org.openoa.base.dto.wecom.WechatConfigProperties.init:40 - [traceId:] ===> wechat config: WechatConfigProperties(zhongqi=WechatConfigProperties.WechatConfig(agentId=1000229, corpId=wwf1c477c862181a7f, corpsecret=IH5PGBOHD1dOMX86TOHi8UclM7uUUzaODKvbmE2m-ZA), chenxing=WechatConfigProperties.WechatConfig(agentId=1000099, corpId=wwe2351ed80f0479d4, corpsecret=_PhZTbZXs6-zucZq1efQXWW5L3ya69RUkTgQRj0eWQk), xinwang=WechatConfigProperties.WechatConfig(agentId=1000129, corpId=wwd674e2ffdc4f6862, corpsecret=kG1XfR6fwFoPBRBIPev92WVw5QtOu2UB0AiNx0TluG4), kuajing=WechatConfigProperties.WechatConfig(agentId=1000060, corpId=ww8bd429f5b618a57e, corpsecret=SFO9FKx4G0Yl_y5E8Jw5cf0DJHmuVisRsOQouayXYkM), jituan=WechatConfigProperties.WechatConfig(agentId=1000008, corpId=wwa0a360c6341c5ae6, corpsecret=GnyB1Y1-SWkutF8_vIDeyGj3WsYqzuDTo9eB5kzJbOg), smhg=WechatConfigProperties.WechatConfig(agentId=999999, corpId=999999, corpsecret=999999), xiaoxia=WechatConfigProperties.WechatConfig(agentId=1000011, corpId=ww71746e109858902c, corpsecret=jAYDpVHoRihTuXLJ8Zm1HPV5M1t9XMi7-861mGdl4rA))
2025-08-27 16:26:59.395 INFO  [main] cn.ce.xxljob.config.XxlAdminAddressConfig.afterPropertiesSet:91 - [traceId:] 处理配置项: xxl.job.admin.addresses:http://pre-omo.aiyouyi.cn/xxl-job-admin/
2025-08-27 16:26:59.395 INFO  [main] cn.ce.xxljob.config.XxlAdminAddressConfig.afterPropertiesSet:109 - [traceId:] Xxl Job Master Admin URL: http://pre-omo.aiyouyi.cn/xxl-job-admin/
2025-08-27 16:26:59.398 INFO  [main] cn.ce.xxljob.config.XxlJobConfig.xxlJobExecutor:46 - [traceId:] >>>>>>>>>>> xxl-job config init.
2025-08-27 16:26:59.643 INFO  [main] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:168 - [traceId:] >>>>>>>>>>> xxl-job register jobhandler success, name:ANTFLOW_SYNC_EHR_EMP_INFO_JOB_HANDLER, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@5c2ae7d7[class org.openoa.job.SyncEhrDataJob#syncEhrEmpInfoJobHandler]
2025-08-27 16:26:59.644 INFO  [main] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:168 - [traceId:] >>>>>>>>>>> xxl-job register jobhandler success, name:ANTFLOW_SYNC_EHR_ORG_INFO_JOB_HANDLER, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6e7cb01a[class org.openoa.job.SyncEhrDataJob#syncEhrOrgInfoJobHandler]
2025-08-27 16:26:59.732 INFO  [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer.start:220 - [traceId:] Tomcat started on port(s): 7001 (http) with context path ''
2025-08-27 16:26:59.742 INFO  [main] org.springframework.boot.StartupInfoLogger.logStarted:61 - [traceId:] Started AntFlowApplication in 8.032 seconds (JVM running for 8.604)
2025-08-27 16:26:59.745 INFO  [main] org.openoa.common.config.StartedListener.onApplicationEvent:25 - [traceId:] your project server started at: http://127.0.0.1:7001
2025-08-27 16:26:59.745 INFO  [main] org.openoa.common.config.StartedListener.onApplicationEvent:27 - [traceId:] your project server was started successfully on port:7001 with context path:/
2025-08-27 16:26:59.745 ERROR [main] org.openoa.AntFlowApplication.main:15 - [traceId:] AntFlow 项目启动成功!
2025-08-27 16:26:59.765 INFO  [Thread-6] com.xxl.job.core.server.EmbedServer$1.run:86 - [traceId:] >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9421
2025-08-27 16:27:04.932 INFO  [http-nio-7001-exec-1] org.apache.juli.logging.DirectJDKLog.log:173 - [traceId:] Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-27 16:27:04.932 INFO  [http-nio-7001-exec-1] org.springframework.web.servlet.FrameworkServlet.initServletBean:525 - [traceId:] Initializing Servlet 'dispatcherServlet'
2025-08-27 16:27:04.933 INFO  [http-nio-7001-exec-1] org.springframework.web.servlet.FrameworkServlet.initServletBean:547 - [traceId:] Completed initialization in 1 ms
2025-08-27 16:27:05.291 INFO  [http-nio-7001-exec-1] org.openoa.aspect.AuthInterceptor.preHandle:25 - [traceId:00000000.1756283224953.664911000] 员工=侯晓瑶, jt83326, 请求路径：/user/queryByNameFuzzy
2025-08-27 16:27:27.492 INFO  [http-nio-7001-exec-2] org.openoa.aspect.AuthInterceptor.preHandle:25 - [traceId:00000000.1756283247320.664911001] 员工=侯晓瑶, jt83326, 请求路径：/user/queryByNameFuzzy
2025-08-27 16:30:13.539 WARN  [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool$HouseKeeper.run:788 - [traceId:] HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=2m48s301ms).
2025-08-27 16:31:04.120 WARN  [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool$HouseKeeper.run:788 - [traceId:] HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=50s581ms).
2025-08-27 16:31:07.323 INFO  [Thread-6] com.xxl.job.core.server.EmbedServer$1.run:96 - [traceId:] >>>>>>>>>>> xxl-job remoting server stop.
2025-08-27 16:31:07.362 INFO  [xxl-job, executor ExecutorRegistryThread] com.xxl.job.core.thread.ExecutorRegistryThread$1.run:87 - [traceId:] >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='antflow-pre', registryValue='http://********:9421/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-08-27 16:31:07.362 INFO  [xxl-job, executor ExecutorRegistryThread] com.xxl.job.core.thread.ExecutorRegistryThread$1.run:105 - [traceId:] >>>>>>>>>>> xxl-job, executor registry thread destory.
2025-08-27 16:31:07.363 INFO  [SpringApplicationShutdownHook] com.xxl.job.core.server.EmbedServer.stop:125 - [traceId:] >>>>>>>>>>> xxl-job remoting server destroy success.
2025-08-27 16:31:07.363 INFO  [xxl-job, executor JobLogFileCleanThread] com.xxl.job.core.thread.JobLogFileCleanThread$1.run:99 - [traceId:] >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2025-08-27 16:31:07.363 INFO  [xxl-job, executor TriggerCallbackThread] com.xxl.job.core.thread.TriggerCallbackThread$1.run:97 - [traceId:] >>>>>>>>>>> xxl-job, executor callback thread destory.
2025-08-27 16:31:07.363 INFO  [Thread-5] com.xxl.job.core.thread.TriggerCallbackThread$2.run:127 - [traceId:] >>>>>>>>>>> xxl-job, executor retry callback thread destory.
2025-08-27 16:31:09.596 INFO  [main] org.springframework.boot.StartupInfoLogger.logStarting:55 - [traceId:] Starting AntFlowApplication using Java 1.8.0_432 on KyleHsudeMacBook-Pro.local with PID 67118 (/Users/<USER>/shuma/source-code/Antflow/antflow-web/target/classes started by kylehsu in /Users/<USER>/shuma/source-code/Antflow)
2025-08-27 16:31:09.597 INFO  [main] org.springframework.boot.SpringApplication.logStartupProfileInfo:638 - [traceId:] The following 1 profile is active: "pre"
2025-08-27 16:31:10.254 INFO  [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate.multipleStoresDetected:262 - [traceId:] Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-27 16:31:10.255 INFO  [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate.registerRepositoriesIn:132 - [traceId:] Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-27 16:31:10.284 INFO  [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate.registerRepositoriesIn:201 - [traceId:] Finished Spring Data repository scanning in 21 ms. Found 0 Redis repository interfaces.
2025-08-27 16:31:10.909 INFO  [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize:108 - [traceId:] Tomcat initialized with port(s): 7001 (http)
2025-08-27 16:31:10.914 INFO  [main] org.apache.juli.logging.DirectJDKLog.log:173 - [traceId:] Starting service [Tomcat]
2025-08-27 16:31:10.915 INFO  [main] org.apache.juli.logging.DirectJDKLog.log:173 - [traceId:] Starting Servlet engine: [Apache Tomcat/9.0.82]
2025-08-27 16:31:10.964 INFO  [main] org.apache.juli.logging.DirectJDKLog.log:173 - [traceId:] Initializing Spring embedded WebApplicationContext
2025-08-27 16:31:10.964 INFO  [main] org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.prepareWebApplicationContext:292 - [traceId:] Root WebApplicationContext: initialization completed in 1232 ms
2025-08-27 16:31:11.851 INFO  [main] com.zaxxer.hikari.HikariDataSource.getConnection:110 - [traceId:] HikariPool-1 - Starting...
2025-08-27 16:31:12.697 INFO  [main] com.zaxxer.hikari.HikariDataSource.getConnection:123 - [traceId:] HikariPool-1 - Start completed.
2025-08-27 16:31:12.906 INFO  [main] org.activiti.engine.impl.ProcessEngineImpl.<init>:86 - [traceId:] ProcessEngine default created
2025-08-27 16:31:12.912 INFO  [main] org.openoa.engine.bpmnconf.activitilistener.BpmnGlobalEventListener.onEvent:45 - [traceId:] AntFlow Engine is started
2025-08-27 16:31:12.912 INFO  [main] org.activiti.engine.impl.cfg.multitenant.MultiSchemaMultiTenantProcessEngineConfiguration.createTenantSchema:160 - [traceId:] creating/validating database schema for tenant 
2025-08-27 16:31:15.358 INFO  [main] org.openoa.base.dto.wecom.WechatConfigProperties.init:40 - [traceId:] ===> wechat config: WechatConfigProperties(zhongqi=WechatConfigProperties.WechatConfig(agentId=1000229, corpId=wwf1c477c862181a7f, corpsecret=IH5PGBOHD1dOMX86TOHi8UclM7uUUzaODKvbmE2m-ZA), chenxing=WechatConfigProperties.WechatConfig(agentId=1000099, corpId=wwe2351ed80f0479d4, corpsecret=_PhZTbZXs6-zucZq1efQXWW5L3ya69RUkTgQRj0eWQk), xinwang=WechatConfigProperties.WechatConfig(agentId=1000129, corpId=wwd674e2ffdc4f6862, corpsecret=kG1XfR6fwFoPBRBIPev92WVw5QtOu2UB0AiNx0TluG4), kuajing=WechatConfigProperties.WechatConfig(agentId=1000060, corpId=ww8bd429f5b618a57e, corpsecret=SFO9FKx4G0Yl_y5E8Jw5cf0DJHmuVisRsOQouayXYkM), jituan=WechatConfigProperties.WechatConfig(agentId=1000008, corpId=wwa0a360c6341c5ae6, corpsecret=GnyB1Y1-SWkutF8_vIDeyGj3WsYqzuDTo9eB5kzJbOg), smhg=WechatConfigProperties.WechatConfig(agentId=999999, corpId=999999, corpsecret=999999), xiaoxia=WechatConfigProperties.WechatConfig(agentId=1000011, corpId=ww71746e109858902c, corpsecret=jAYDpVHoRihTuXLJ8Zm1HPV5M1t9XMi7-861mGdl4rA))
2025-08-27 16:31:16.329 INFO  [main] cn.ce.xxljob.config.XxlAdminAddressConfig.afterPropertiesSet:91 - [traceId:] 处理配置项: xxl.job.admin.addresses:http://pre-omo.aiyouyi.cn/xxl-job-admin/
2025-08-27 16:31:16.329 INFO  [main] cn.ce.xxljob.config.XxlAdminAddressConfig.afterPropertiesSet:109 - [traceId:] Xxl Job Master Admin URL: http://pre-omo.aiyouyi.cn/xxl-job-admin/
2025-08-27 16:31:16.332 INFO  [main] cn.ce.xxljob.config.XxlJobConfig.xxlJobExecutor:46 - [traceId:] >>>>>>>>>>> xxl-job config init.
2025-08-27 16:31:16.575 INFO  [main] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:168 - [traceId:] >>>>>>>>>>> xxl-job register jobhandler success, name:ANTFLOW_SYNC_EHR_EMP_INFO_JOB_HANDLER, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@36ce0bc2[class org.openoa.job.SyncEhrDataJob#syncEhrEmpInfoJobHandler]
2025-08-27 16:31:16.575 INFO  [main] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:168 - [traceId:] >>>>>>>>>>> xxl-job register jobhandler success, name:ANTFLOW_SYNC_EHR_ORG_INFO_JOB_HANDLER, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@317fa5dd[class org.openoa.job.SyncEhrDataJob#syncEhrOrgInfoJobHandler]
2025-08-27 16:31:16.651 INFO  [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer.start:220 - [traceId:] Tomcat started on port(s): 7001 (http) with context path ''
2025-08-27 16:31:16.658 INFO  [main] org.springframework.boot.StartupInfoLogger.logStarted:61 - [traceId:] Started AntFlowApplication in 7.355 seconds (JVM running for 7.669)
2025-08-27 16:31:16.660 INFO  [main] org.openoa.common.config.StartedListener.onApplicationEvent:25 - [traceId:] your project server started at: http://127.0.0.1:7001
2025-08-27 16:31:16.660 INFO  [main] org.openoa.common.config.StartedListener.onApplicationEvent:27 - [traceId:] your project server was started successfully on port:7001 with context path:/
2025-08-27 16:31:16.660 ERROR [main] org.openoa.AntFlowApplication.main:15 - [traceId:] AntFlow 项目启动成功!
2025-08-27 16:31:16.680 INFO  [Thread-6] com.xxl.job.core.server.EmbedServer$1.run:86 - [traceId:] >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9421
2025-08-27 16:31:31.303 INFO  [http-nio-7001-exec-1] org.apache.juli.logging.DirectJDKLog.log:173 - [traceId:] Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-27 16:31:31.303 INFO  [http-nio-7001-exec-1] org.springframework.web.servlet.FrameworkServlet.initServletBean:525 - [traceId:] Initializing Servlet 'dispatcherServlet'
2025-08-27 16:31:31.304 INFO  [http-nio-7001-exec-1] org.springframework.web.servlet.FrameworkServlet.initServletBean:547 - [traceId:] Completed initialization in 1 ms
2025-08-27 16:31:31.698 INFO  [http-nio-7001-exec-1] org.openoa.aspect.AuthInterceptor.preHandle:25 - [traceId:00000000.1756283491324.671181000] 员工=侯晓瑶, jt83326, 请求路径：/user/queryByNameFuzzy
2025-08-27 16:32:02.380 INFO  [http-nio-7001-exec-2] org.openoa.aspect.AuthInterceptor.preHandle:25 - [traceId:00000000.1756283522211.671181001] 员工=侯晓瑶, jt83326, 请求路径：/user/queryByNameFuzzy
2025-08-27 16:32:11.515 INFO  [http-nio-7001-exec-3] org.openoa.aspect.AuthInterceptor.preHandle:25 - [traceId:00000000.1756283531340.671181002] 员工=侯晓瑶, jt83326, 请求路径：/user/queryByNameFuzzy
2025-08-27 16:32:27.571 INFO  [Thread-6] com.xxl.job.core.server.EmbedServer$1.run:96 - [traceId:] >>>>>>>>>>> xxl-job remoting server stop.
2025-08-27 16:32:27.678 INFO  [xxl-job, executor ExecutorRegistryThread] com.xxl.job.core.thread.ExecutorRegistryThread$1.run:87 - [traceId:] >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='antflow-pre', registryValue='http://********:9421/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-08-27 16:32:27.678 INFO  [xxl-job, executor ExecutorRegistryThread] com.xxl.job.core.thread.ExecutorRegistryThread$1.run:105 - [traceId:] >>>>>>>>>>> xxl-job, executor registry thread destory.
2025-08-27 16:32:27.678 INFO  [SpringApplicationShutdownHook] com.xxl.job.core.server.EmbedServer.stop:125 - [traceId:] >>>>>>>>>>> xxl-job remoting server destroy success.
2025-08-27 16:32:27.678 INFO  [xxl-job, executor JobLogFileCleanThread] com.xxl.job.core.thread.JobLogFileCleanThread$1.run:99 - [traceId:] >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2025-08-27 16:32:27.679 INFO  [xxl-job, executor TriggerCallbackThread] com.xxl.job.core.thread.TriggerCallbackThread$1.run:97 - [traceId:] >>>>>>>>>>> xxl-job, executor callback thread destory.
2025-08-27 16:32:27.679 INFO  [Thread-5] com.xxl.job.core.thread.TriggerCallbackThread$2.run:127 - [traceId:] >>>>>>>>>>> xxl-job, executor retry callback thread destory.
