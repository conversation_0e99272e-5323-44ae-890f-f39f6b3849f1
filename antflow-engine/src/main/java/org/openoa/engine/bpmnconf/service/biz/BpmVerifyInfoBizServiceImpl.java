package org.openoa.engine.bpmnconf.service.biz;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Multimap;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.HistoryService;
import org.activiti.engine.history.HistoricProcessInstance;
import org.activiti.engine.history.HistoricTaskInstance;
import org.activiti.engine.history.HistoricVariableInstance;
import org.activiti.engine.impl.pvm.PvmActivity;
import org.activiti.engine.impl.pvm.process.ActivityImpl;
import org.activiti.engine.task.TaskInfo;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.openoa.base.constant.enums.ProcessDisagreeTypeEnum;
import org.openoa.base.constant.enums.SignTypeEnum;
import org.openoa.base.dto.wecom.NodeHeadActionDto;
import org.openoa.base.entity.BpmBusinessProcess;
import org.openoa.base.entity.User;
import org.openoa.base.mapper.UserMapper;
import org.openoa.base.service.empinfoprovider.BpmnEmployeeInfoProviderService;
import org.openoa.base.vo.BaseIdTranStruVo;
import org.openoa.base.vo.BpmVerifyInfoVo;
import org.openoa.base.vo.BpmnConfCommonElementVo;
import org.openoa.common.entity.BpmVariableMultiplayer;
import org.openoa.common.entity.BpmVariableMultiplayerPersonnel;
import org.openoa.common.entity.BpmVariableSingle;
import org.openoa.common.service.BpmVariableMultiplayerPersonnelServiceImpl;
import org.openoa.common.service.BpmVariableMultiplayerServiceImpl;
import org.openoa.common.service.BpmVariableSingleServiceImpl;
import org.openoa.engine.bpmnconf.common.ActivitiAdditionalInfoServiceImpl;
import org.openoa.engine.bpmnconf.confentity.*;
import org.openoa.engine.bpmnconf.mapper.BpmBusinessProcessMapper;
import org.openoa.engine.bpmnconf.mapper.ProcessApprovalMapper;
import org.openoa.engine.bpmnconf.service.impl.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static org.openoa.base.constant.enums.ProcessEnum.COMLETE_STATE;
import static org.openoa.base.constant.enums.ProcessNodeEnum.START_TASK_KEY;

/**
 * <AUTHOR>
 * @Description verify info biz service
 * @Param
 * @return
 * @Version 0.5
 * BpmVerifyInfoNewServiceImpl
 */
@Slf4j
@Service
public class BpmVerifyInfoBizServiceImpl extends BizServiceImpl<BpmVerifyInfoServiceImpl> {
    @Autowired
    private BpmBusinessProcessServiceImpl bpmBusinessProcessService;
    @Autowired
    private HistoryService historyService;
    @Autowired
    private BpmVariableServiceImpl bpmVariableService;
    @Autowired
    private ActivitiAdditionalInfoServiceImpl activitiAdditionalInfoService;
    @Autowired
    private BpmVariableSignUpServiceImpl bpmVariableSignUpService;
    @Autowired
    private BpmVariableSignUpPersonnelServiceImpl bpmVariableSignUpPersonnelService;
    @Autowired
    private BpmVariableSingleServiceImpl bpmVariableSingleService;
    @Autowired
    private BpmVariableMultiplayerServiceImpl bpmVariableMultiplayerService;
    @Autowired
    private BpmVariableMultiplayerPersonnelServiceImpl bpmVariableMultiplayerPersonnelService;
    @Autowired
    private BpmnEmployeeInfoProviderService employeeInfoProvider;
    @Resource
    private BpmFlowrunEntrustServiceImpl bpmFlowrunEntrustService;
	@Resource
	private BpmProcessNodeSubmitServiceImpl bpmProcessNodeSubmitService;
	@Resource
	private UserMapper userMapper;
	@Autowired
	private BpmVerifyInfoServiceImpl bpmVerifyInfoServiceImpl;
	@Autowired
	private ProcessApprovalMapper processApprovalMapper;
	@Autowired
	private BpmnNodePersonnelConfServiceImpl bpmnNodePersonnelConfService;
	@Autowired
	private BpmnNodePersonnelEmplConfServiceImpl bpmnNodePersonnelEmplConfService;
	@Autowired
	private BpmBusinessProcessMapper businessProcessMapper;
	@Autowired
	private BpmnConfCommonServiceImpl bpmnConfCommonService;
	@Autowired
	private BpmnConfServiceImpl bpmnConfService;

	public List<BpmVerifyInfoVo> getVerifyInfoList(String processCode) {
        List<BpmVerifyInfoVo> bpmVerifyInfoVos = service.verifyInfoList(processCode);
        return bpmVerifyInfoVos;
    }

    /**
     * get verify path for a process
     *
     * @param processNumber process Number
     * @param finishFlag    to indicate whether a process is finished true for finished and false for not finished yet
     * @return verify path include finished and unfinished nodes
     */
    public List<BpmVerifyInfoVo> getBpmVerifyInfoVos(String processNumber, boolean finishFlag) {
	    List<String> msConfNode = Lists.newArrayList("分公司HRBP面试时间再次确认", "商务经理现场面试", "区域招聘专员沟通试岗时间", "分司HRBP确认已来试岗", "区域培训经理考核试岗", "商务经理确认试岗结果", "分公司总监终试结果确认");
	    List<BpmVerifyInfoVo> bpmVerifyInfoVos = Lists.newArrayList();

        //query business process info
        BpmBusinessProcess bpmBusinessProcess = bpmBusinessProcessService.getBaseMapper().selectOne(new QueryWrapper<BpmBusinessProcess>().eq("BUSINESS_NUMBER", processNumber));


        if (ObjectUtils.isEmpty(bpmBusinessProcess)) {
            return bpmVerifyInfoVos;
        }


        //add start node for process
        bpmVerifyInfoVos.add(BpmVerifyInfoVo.builder().taskName("发起").verifyStatus(1).verifyUserIds(Lists.newArrayList(bpmBusinessProcess.getCreateUser())).verifyUserName(bpmBusinessProcess.getUserName()).verifyDate(bpmBusinessProcess.getCreateTime()).verifyStatusName("提交").build());


        //query and then append process record
        List<BpmVerifyInfoVo> searchBpmVerifyInfoVos = service.verifyInfoList(processNumber, bpmBusinessProcess.getProcInstId());

        //sort verify info by verify date in ascending order
        searchBpmVerifyInfoVos = searchBpmVerifyInfoVos.stream().sorted(Comparator.comparing(BpmVerifyInfoVo::getVerifyDate)).collect(Collectors.toList());
        bpmVerifyInfoVos.addAll(searchBpmVerifyInfoVos);


        //query process's historic process instance
        HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery().processInstanceId(bpmBusinessProcess.getProcInstId()).singleResult();

        //query from the process engine to get the last approval record
	    List<HistoricTaskInstance> historics = historyService.createHistoricTaskInstanceQuery().processInstanceId(historicProcessInstance.getId()).orderByHistoricTaskInstanceEndTime().desc().list();
	    HistoricTaskInstance lastHistoricTaskInstance = historics.get(0);

        //begin to sort the verify info
        int sort = 0;

        //iterate through the verify info
	    //审批拒绝
	    Boolean noApproval = false;
	    boolean hasBack;
		String originalApproverId;
	    List<HistoricTaskInstance> userFilterApproverList = Lists.newArrayList();
		    List<BpmVerifyInfoVo> bpmVerifyInfoSortVos = Lists.newArrayList();
        for (BpmVerifyInfoVo bpmVerifyInfoVo : bpmVerifyInfoVos) {
            if (bpmVerifyInfoVo.getVerifyStatus() == 3 || bpmVerifyInfoVo.getVerifyStatus() == 6) {
                bpmVerifyInfoVo.setTaskName(lastHistoricTaskInstance.getName());

                bpmVerifyInfoVo.setVerifyStatusName("审批拒绝");
	            noApproval = true; //有审批拒绝，则流程结束
            }

            if (bpmVerifyInfoVo.getVerifyStatus() == 5) {
                //todo 待实现

                String lastAssignee = lastHistoricTaskInstance.getAssignee();
                String lastAssigneeName = lastHistoricTaskInstance.getAssigneeName();

                BpmVerifyInfoVo vo = new BpmVerifyInfoVo();
                BeanUtils.copyProperties(bpmVerifyInfoVo, vo);
                vo.setTaskName("发起人");
                vo.setVerifyUserIds(Lists.newArrayList(bpmBusinessProcess.getCreateUser()));
                vo.setVerifyUserName(bpmBusinessProcess.getUserName());
                vo.setVerifyDate(vo.getVerifyDate());
                vo.setSort(sort);
                sort++;
                bpmVerifyInfoSortVos.add(vo);


                bpmVerifyInfoVo.setTaskName(lastHistoricTaskInstance.getName());
                bpmVerifyInfoVo.setVerifyUserId(lastAssignee);
                if (!StringUtils.isEmpty(lastAssigneeName)) {
                    bpmVerifyInfoVo.setVerifyUserName(lastAssigneeName);
                } else {
                    Map<String, String> provideEmployeeInfo = employeeInfoProvider.provideEmployeeInfo(Lists.newArrayList(lastAssignee));
                    bpmVerifyInfoVo.setVerifyUserName(provideEmployeeInfo.get(lastAssignee));
                }

                bpmVerifyInfoVo.setVerifyDate(null);
                bpmVerifyInfoVo.setVerifyDesc(StringUtils.EMPTY);
                bpmVerifyInfoVo.setVerifyStatus(0);
                bpmVerifyInfoVo.setVerifyStatusName(StringUtils.EMPTY);
            }

            bpmVerifyInfoVo.setSort(sort);
            bpmVerifyInfoSortVos.add(bpmVerifyInfoVo);
            sort++;
        }

        bpmVerifyInfoVos.clear();
        bpmVerifyInfoVos.addAll(bpmVerifyInfoSortVos);


        //query to do task info
        List<BpmVerifyInfoVo> taskInfo = service.findTaskInfo(bpmBusinessProcess);

        BpmVerifyInfoVo taskVo;
        if (!ObjectUtils.isEmpty(taskInfo) && !finishFlag) {

            //append to do task info
            taskVo = taskInfo.get(0);
            taskVo.setSort(sort);
            taskVo.setVerifyStatus(99);
            taskVo.setVerifyStatusName("处理中");
	        NodeHeadActionDto headAction = businessProcessMapper.getHeadAction(processNumber, taskVo.getElementId());
			if (headAction != null && Objects.nonNull(headAction.getNodeId())) {
				List<String> hasApprovedUserNames = bpmVerifyInfoVos.stream().map(BpmVerifyInfoVo::getVerifyUserName).distinct().collect(Collectors.toList());
				BpmnNodePersonnelConf nodePer = bpmnNodePersonnelConfService.lambdaQuery()
					.eq(BpmnNodePersonnelConf::getBpmnNodeId, headAction.getNodeId())
					.eq(BpmnNodePersonnelConf::getSignType, SignTypeEnum.SIGN_TYPE_SIGN_IN_ORDER.getCode())
					.last("limit 1").one();
				if (Objects.nonNull(nodePer)) {
					List<BpmnNodePersonnelEmplConf> signTypeThreeEmpList = bpmnNodePersonnelEmplConfService.lambdaQuery().eq(BpmnNodePersonnelEmplConf::getBpmnNodePersonneId, nodePer.getId()).list();
					if (!CollectionUtils.isEmpty(signTypeThreeEmpList) && signTypeThreeEmpList.size() > 1) {
						taskVo.setSignTypeThree(Boolean.TRUE); // 处理中的节点是否为顺序会签
						taskVo.setVerifyUserIds(Lists.transform(signTypeThreeEmpList, BpmnNodePersonnelEmplConf::getEmplId));
						String signTypeThreeNodeName = signTypeThreeEmpList.stream().map(BpmnNodePersonnelEmplConf::getEmplName).collect(Collectors.joining(", "));
						List<String> signUserNames = signTypeThreeEmpList.stream().map(BpmnNodePersonnelEmplConf::getEmplName).collect(Collectors.toList());
						List<String> approvedSigners = hasApprovedUserNames.stream().filter(signUserNames::contains).collect(Collectors.toList());
						// 如果已审批过的节点（hasApprovedUserNames）中有被signTypeThreeNodeName contains的元素，那就不用显示会签的全部人名，因为此时已经被其中的一个人审批过了
						taskVo.setVerifyUserName(CollectionUtils.isEmpty(approvedSigners) ? signTypeThreeNodeName : taskVo.getVerifyUserName());
					}
				}
			}
	        bpmVerifyInfoVos.add(taskVo);

            List<BpmFlowrunEntrust> flowrunEntrustList = bpmFlowrunEntrustService.list(
                    Wrappers.<BpmFlowrunEntrust>lambdaQuery().eq(BpmFlowrunEntrust::getRuntaskid, taskVo.getId()).orderByDesc(BpmFlowrunEntrust::getId));
            if(!CollectionUtils.isEmpty(flowrunEntrustList)){
                BpmFlowrunEntrust flowrunEntrust = flowrunEntrustList.get(0);
                String actual = flowrunEntrust.getActual();
                if(taskVo.getVerifyUserId().equals(actual)){
	                originalApproverId = flowrunEntrust.getOriginal();
                    String actualVerifyUserName = taskVo.getVerifyUserName();
                    String originalName = flowrunEntrust.getOriginalName();
                    taskVo.setVerifyUserName(actualVerifyUserName+" 代 "+originalName+" 审批 ");
	                User user = userMapper.getUserById(actual);
					if (Objects.nonNull(user)) {
						User defaultVerifyUser = userMapper.getDefaultNodeHeaderActionUser(user.getEhrSource());
						if (Objects.nonNull(defaultVerifyUser) && Objects.equals(actual, defaultVerifyUser.getId())) {
							taskVo.setVerifyUserName("审核人为空，移交管理员");
						}
					}
                } else {originalApproverId = "";}
            } else {originalApproverId = "";}
	        sort++;


            // if the task node is start node, then it means the process is returned and the last approval record is appended to the historics
	        if (taskVo.getElementId().equals(START_TASK_KEY.getDesc())) {
		        List<HistoricTaskInstance> allApprovers = historics.stream().filter(h -> !Objects.equals(h.getTaskDefinitionKey(), START_TASK_KEY.getDesc())).sorted(Comparator.comparing(HistoricTaskInstance::getStartTime)).collect(Collectors.toList());
		        List<Pair<String, Integer>> backPairList = getNodeKeyAndBackTypeByProcessNumber(processNumber);
		        if (!CollectionUtils.isEmpty(backPairList)) {
			        hasBack = true;
			        if (backPairList.size() == 1) {
				        allApprovers = filterNodeKeyPair(backPairList.get(0), allApprovers);
				        userFilterApproverList = allApprovers;
			        } else {
				        for (Pair<String, Integer> stringIntegerPair : backPairList) {
					        allApprovers = filterNodeKeyPair(stringIntegerPair, allApprovers);
					        userFilterApproverList = allApprovers;
				        }
			        }
		        } else {hasBack = false;}
		        for (HistoricTaskInstance approver : allApprovers) {
			        taskVo = new BpmVerifyInfoVo();
			        taskVo.setElementId(approver.getTaskDefinitionKey());
			        taskVo.setTaskName(approver.getName());
			        taskVo.setVerifyStatus(0);
			        String lastAssignee = approver.getAssignee();
			        Map<String, String> provideEmployeeInfo = employeeInfoProvider.provideEmployeeInfo(Lists.newArrayList(lastAssignee));
			        String lastVerifierName = provideEmployeeInfo.get(lastAssignee);
			        taskVo.setVerifyUserId(lastAssignee);
			        taskVo.setVerifyUserName(lastVerifierName);
			        bpmVerifyInfoVos.add(taskVo);
			        sort++;
		        }
	        }
			else {
		        BpmVerifyInfoVo finalTaskVo = taskVo;
		        List<HistoricTaskInstance> allApprovers = historics.stream().filter(h -> !Objects.equals(h.getTaskDefinitionKey(), START_TASK_KEY.getDesc()) && !Objects.equals(h.getTaskDefinitionKey(), finalTaskVo.getElementId())).sorted(Comparator.comparing(HistoricTaskInstance::getStartTime)).collect(Collectors.toList());
				// 或签节点，只显示一个或签节点，里面的审批人不单独返回
				List<BpmVerifyInfoVo> multipleApproversOnOneNode = bpmVerifyInfoVos.stream().filter(x -> !CollectionUtils.isEmpty(x.getVerifyUserIds())).collect(Collectors.toList());
				if (!CollectionUtils.isEmpty(multipleApproversOnOneNode)) {
					for (BpmVerifyInfoVo bpmVerifyInfoVo : multipleApproversOnOneNode) {
						allApprovers = allApprovers.stream().filter(x -> !bpmVerifyInfoVo.getVerifyUserIds().contains(x.getAssignee())).collect(Collectors.toList());
					}
				}
		        // JTS-15 过滤掉 退回任意类型的节点，然后再返回当前的节点中间的节点（如a->b->c->d->e审核 d审核时退回给了b，那么b审核后直接回到d，审核记录中的待审核人里不再显示c）
		        List<Pair<String, Integer>> backPairList = getNodeKeyAndBackTypeByProcessNumber(processNumber);
				if (!CollectionUtils.isEmpty(backPairList)) {
					hasBack = true;
					if (backPairList.size() == 1) {
						allApprovers = filterNodeKeyPair(backPairList.get(0), allApprovers);
					} else {
						for (Pair<String, Integer> stringIntegerPair : backPairList) {
							allApprovers = filterNodeKeyPair(stringIntegerPair, allApprovers);
						}
					}
				} else {hasBack = false;}

		        for (HistoricTaskInstance approver : allApprovers) {
			        taskVo = new BpmVerifyInfoVo();
			        taskVo.setElementId(approver.getTaskDefinitionKey());
			        taskVo.setTaskName(approver.getName());
			        taskVo.setVerifyStatus(0);
			        String lastAssignee = approver.getAssignee();
			        Map<String, String> provideEmployeeInfo = employeeInfoProvider.provideEmployeeInfo(Lists.newArrayList(lastAssignee));
			        String lastVerifierName = provideEmployeeInfo.get(lastAssignee);
			        taskVo.setVerifyUserId(lastAssignee);
			        taskVo.setVerifyUserName(lastVerifierName);
			        // 正常审批，过滤已审批过的重复值
			        if (allApprovers.stream().noneMatch(x -> Objects.equals(approver.getAssigneeName(), x.getAssigneeName()) && Objects.equals(x.getName(), approver.getName()))) {
				        // bpmVerifyInfoVos过滤掉第一个发起人后，再获取里面的verifyStatus是不是都等于2
				        if (bpmVerifyInfoVos.stream().skip(1).allMatch(vo -> vo.getVerifyStatus() != null && vo.getVerifyStatus() == 2)) {
							bpmVerifyInfoVos.add(taskVo);
				        }
			        } else if (hasBack) {
				        bpmVerifyInfoVos.add(taskVo);
			        }
			        sort++;
		        }
	        }
        } else {
	        originalApproverId = "";
	        hasBack = false;
	        taskVo = new BpmVerifyInfoVo();
            taskVo.setElementId(lastHistoricTaskInstance.getTaskDefinitionKey());
        }
		// 已审批过的节点最后一个是否为加批节点
	    boolean hasVerifyedLastIsJp = bpmVerifyInfoVos.stream()
		    .filter(x -> Objects.nonNull(x.getVerifyDate()))
		    .max(Comparator.comparing(BpmVerifyInfoVo::getVerifyDate))
		    .map(vo -> Objects.equals("加批", vo.getVerifyStatusName()))
		    .orElse(false);
	    //追加流程记录
	    if (!finishFlag) {
		    if(!noApproval){
			    //当节点没有审批拒绝时，才追加流程记录
                addBpmVerifyInfoVo(processNumber, sort, bpmVerifyInfoVos, historicProcessInstance, taskVo);
            }
	    }

	    Integer endVerifyStatus = 100;
	    if (bpmBusinessProcess.getProcessState() == COMLETE_STATE.getCode()) {
		    endVerifyStatus = 0;
        }

        bpmVerifyInfoVos.add(BpmVerifyInfoVo.builder().taskName("流程结束").verifyStatus(endVerifyStatus).build());
		log.info("侧边栏审批记录，未过滤前={}", JSON.toJSONString(bpmVerifyInfoVos));
		// JTS-19 如果bpmVerifyInfoVos中的verifyUserName包含"||"并且"||"前后的字符串一样的话，就过滤掉
	    List<BpmVerifyInfoVo> finalBpmVerifyInfoVos = bpmVerifyInfoVos;
	    List<HistoricTaskInstance> finalUserFilterApproverList = userFilterApproverList;
	    List<BpmVerifyInfoVo> orNodes = finalBpmVerifyInfoVos.stream().filter(x -> StringUtils.isNotBlank(x.getVerifyUserName()) && StringUtils.isNotBlank(x.getTaskName()))
		    .filter(x -> x.getTaskName().contains("||") && x.getElementId().contains(",")).collect(Collectors.toList());
	    AtomicReference<Integer> zhuanbanNextNodeSort = new AtomicReference<>(0);
	    List<BpmVerifyInfoVo> handling = bpmVerifyInfoVos.stream().filter(x -> x.getVerifyStatus().equals(99)).collect(Collectors.toList());

	    BpmVerifyInfoVo handlingNode =  CollectionUtils.isEmpty(handling) ?  null : handling.get(0);
	    // Boolean hasIntersection = previewNode.getHasIntersection();
	    bpmVerifyInfoVos = bpmVerifyInfoVos.stream()
		    .filter(vo -> {
			    String name = vo.getVerifyUserName();
			    if (name != null && name.contains("||")) {
				    String[] parts = name.split("\\|\\|", -1); // -1 保证保留空字符串
				    return parts.length != 2 || !Objects.equals(parts[0], parts[1]);
			    }
			    return true;
		    })
		    // 再加一个filter 如果有相同的verifyUserIds和verifyUserName，就过滤掉后面的相同的且verifyUserIds不为空的元素
		    .filter(vo -> {
                if (vo.getVerifyUserIds() != null && !vo.getVerifyUserIds().isEmpty()) {
                return finalBpmVerifyInfoVos.stream()
                  .filter(v -> Objects.equals(v.getVerifyUserIds(), vo.getVerifyUserIds()) &&
                          Objects.equals(v.getVerifyUserName(), vo.getVerifyUserName()) && Optional.ofNullable(vo.getVerifyUserId()).orElse("").contains(","))
                  .findFirst()
                  .map(v -> v == vo)
                  .orElse(true);
		          }
		          return true;
	        })
		    // 针对有退回的情况再加一个filter 如果有相同的verifyUserIds和verifyUserName，就过滤掉后面的相同的且hasBack=true的元素
		    .filter(vo -> {
			    if (hasBack && !CollectionUtils.isEmpty(finalUserFilterApproverList)) {
				    return finalBpmVerifyInfoVos.stream()
					    .filter(v -> Objects.equals(v.getVerifyUserId(), vo.getVerifyUserId()) &&
						    Objects.equals(v.getVerifyUserName(), vo.getVerifyUserName()) && !Lists.transform(finalUserFilterApproverList, TaskInfo::getAssignee).contains(vo.getVerifyUserId()))
					    .findFirst()
					    .map(v -> v == vo)
					    .orElse(true);
			    }
				// if (finalBpmVerifyInfoVos.stream().anyMatch(v -> Objects.equals(vo.getVerifyUserName(), v.getVerifyUserName()) && Objects.equals(vo.getVerifyStatus(), 0) && Objects.equals(v.getTaskName(), vo.getTaskName())) && StringUtils.isBlank(vo.getTaskDefKey())) {
				// 	return false;
			    // }
			    if (msConfNode.contains(vo.getTaskName()) && Objects.equals(vo.getVerifyStatus(), 0) && StringUtils.isBlank(vo.getTaskDefKey()) && finalBpmVerifyInfoVos.stream().filter(v -> v.getVerifyStatus() != 0).anyMatch(v -> Objects.equals(v.getTaskName(), vo.getTaskName()))) {
				    return hasVerifyedLastIsJp;
			    }
			    return true;
		    })
		    .filter(vo -> { // 未处理的task里如果有多人且重复的节点
			    boolean nodeNameFilter = vo.getTaskName().contains("会签") || vo.getTaskName().contains("或签");
			    if (Objects.equals(vo.getVerifyStatus(), 0) && StringUtils.isNotBlank(vo.getVerifyUserName())) {
				    if (vo.getVerifyUserName().contains(",") || vo.getVerifyUserName().contains("||") || nodeNameFilter) {
					    if (!CollectionUtils.isEmpty(orNodes)) { // 为了过滤掉或签的多个重复节点
						    return !orNodes.get(0).getTaskName().contains(vo.getTaskName());
					    }
					    // 找出finalBpmVerifyInfoVos中并且verifyUserName包含英文逗号的、有taskName字段相同的并且elementId字段相同的,作为应该过滤的节点，包含了该过滤的节点就过滤掉
					    List<BpmVerifyInfoVo> shouldFilteredNode = finalBpmVerifyInfoVos.stream().filter(x -> x.getTaskName() != null && x.getElementId() != null && x.getVerifyUserName() != null)
						    .filter(x -> vo.getVerifyUserName().contains(","))
						    .collect(Collectors.groupingBy(x -> x.getTaskName() + "|" + vo.getElementId())).values()
						    .stream().filter(list -> list.size() > 1) // 只要taskName和elementId相同的组，数量大于1
						    .flatMap(Collection::stream).collect(Collectors.toList());
						boolean endduorenhuiqian = false;
						if (vo.getVerifyUserName().contains(",")) {
							String[] split = vo.getVerifyUserName().split(",");
							for (String s : split) { // split是一个节点有多个人的情况，如果这多个人都在已审核过的节点中出现过了，endduorenhuiqian就置位true，endduorenhuiqian只在顺序会签的时候判断
								if (finalBpmVerifyInfoVos.stream().filter(x -> !Objects.equals(0, x.getVerifyStatus())).noneMatch(x -> Objects.equals(x.getVerifyUserName(), s))) {
									endduorenhuiqian = true;
									break;
								}
							}
						}
					    return !shouldFilteredNode.contains(vo) || (Objects.equals(true, vo.getSignTypeThree()) && endduorenhuiqian);
				    }
				    return true;
			    }
			    return true;
		    })
		    .filter(vo -> {
				if (vo.getVerifyUserName() != null && vo.getVerifyUserName().contains("代") && vo.getVerifyUserName().contains("审批")) { // 找到设置待转办的节点,记录下来这个节点的sort，把sort后面里节点审核人的id与originalApproverId做判断
					zhuanbanNextNodeSort.set(vo.getSort());
				}
				if (vo.getSort() != null && !CollectionUtils.isEmpty(vo.getVerifyUserIds())) {
					if (vo.getSort() > zhuanbanNextNodeSort.get() && vo.getVerifyUserIds().contains(originalApproverId)) {
						return false;
					}
				}
			    return true;
		    })
		    // 多个顺序会签节点，去掉待处理的顺序会签节点
			.filter(x ->  {
				if (x.getVerifyStatus().equals(99) || x.getVerifyDate() != null) { // 状态=99表示待审核的，有审核时间代表已经审核过的，这两种情况不用过滤
					return true;
				} else if (handlingNode != null && x.getVerifyUserIds() != null && x.getVerifyUserIds().contains(",")) { // 只有待审核的节点，需要过滤掉重复的节点
					return !Objects.equals(x.getTaskName(), handlingNode.getTaskName()) && !Objects.equals(x.getElementId(), handlingNode.getElementId());
				}
				return true;
			})
		    .collect(Collectors.toList());
        return bpmVerifyInfoVos;
    }

	private static List<HistoricTaskInstance> filterNodeKeyPair(Pair<String, Integer> backPair, List<HistoricTaskInstance> allApprovers) {
		allApprovers = allApprovers.stream().filter(x ->
			Objects.equals(x.getTaskDefinitionKey(), backPair.getLeft()) && Arrays.asList(ProcessDisagreeTypeEnum.THREE_DISAGREE.getCode(), ProcessDisagreeTypeEnum.FIVE_DISAGREE.getCode()).contains(backPair.getRight())
		).collect(Collectors.toList());
		return allApprovers;
	}

	/**
     * append info
     *
     * @param processNumber
     * @param sort
     * @param bpmVerifyInfoVos
     * @param historicProcessInstance
     * @param taskVo
     */
    private void addBpmVerifyInfoVo(String processNumber, Integer sort, List<BpmVerifyInfoVo> bpmVerifyInfoVos, HistoricProcessInstance historicProcessInstance, BpmVerifyInfoVo taskVo) {

        //get all activiti flow nodes list
        List<ActivityImpl> activitiList = activitiAdditionalInfoService.getActivitiList(historicProcessInstance);

        //query process variable info
        BpmVariable bpmVariable = bpmVariableService.getBaseMapper().selectOne(new QueryWrapper<BpmVariable>().eq("process_num", processNumber));

        if (ObjectUtils.isEmpty(bpmVariable)) {
            return;
        }
        //get approvers
        Map<String, List<BaseIdTranStruVo>> nodeApproveds = getNodeApproveds(bpmVariable.getId());

        List<ActivityImpl> collect = new ArrayList<>(activitiList);

        if (!collect.isEmpty()) {

            ActivityImpl activity = collect.get(0);
            Map<String, Object> properties = activity.getProperties();
            Object multiInstance = properties.get("multiInstance");
            if("sequential".equals(multiInstance)){
                List<BaseIdTranStruVo> baseIdTranStruVos = nodeApproveds.get(taskVo.getElementId());

                List<BpmVerifyInfoVo> verifyInfoVos = new ArrayList<>(bpmVerifyInfoVos);

                List<BaseIdTranStruVo> idTranStruVos=new ArrayList<>();

                if(!CollectionUtils.isEmpty(baseIdTranStruVos)){
                    for (BaseIdTranStruVo baseIdTranStruVo : baseIdTranStruVos) {

                        for (BpmVerifyInfoVo verifyInfoVo : verifyInfoVos) {
                            if(!verifyInfoVo.getVerifyUserIds().contains(baseIdTranStruVo.getId())){
                                idTranStruVos.add(baseIdTranStruVo);
                            }

                        }
                    }
                }

                for (BaseIdTranStruVo idTranStruVo : idTranStruVos) {
                    BpmVerifyInfoVo bpmVerifyInfoVo = BpmVerifyInfoVo.builder()
                            .verifyUserId(idTranStruVo.getId())
                            .verifyUserIds(Lists.newArrayList(idTranStruVo.getId()))
                            .elementId(taskVo.getElementId())
                            .taskName(taskVo.getTaskName())
                            .verifyUserName(idTranStruVo.getName())
                            .verifyStatus(0)
                            .sort(sort)
                            .build();
                    bpmVerifyInfoVos.add(bpmVerifyInfoVo);
                    sort++;
                }


            };
        }

        //get signup node's element id and collection name
        Map<String, String> signUpNodeCollectionNameMap = getSignUpNodeCollectionNameMap(bpmVariable.getId());

        //todo 需要实现
        //get employee id map

        //variable name 2 HistoricVariableInstance
        Multimap<String, HistoricVariableInstance> variableInstanceMap = activitiAdditionalInfoService.getVariableInstanceMap(historicProcessInstance.getId());

        //do append record
        doAddBpmVerifyInfoVo(sort, taskVo.getElementId(), activitiList, nodeApproveds, signUpNodeCollectionNameMap, bpmVerifyInfoVos, variableInstanceMap, bpmVariable.getId());
    }

    /**
     * to process the signup special node
     *
     * @param variableId
     * @return
     */
    public Map<String, String> getSignUpNodeCollectionNameMap(Long variableId) {

        Map<String, String> signUpNodeCollectionNameMap = Maps.newHashMap();

        List<BpmVariableSignUp> bpmVariableSignUps = bpmVariableSignUpService.getBaseMapper().selectList(new QueryWrapper<BpmVariableSignUp>().eq("variable_id", variableId));

        for (BpmVariableSignUp variableSignUp : bpmVariableSignUps) {
            if (!ObjectUtils.isEmpty(variableSignUp.getSubElements())) {
                List<BpmnConfCommonElementVo> bpmnConfCommonElementVos = JSON.parseArray(variableSignUp.getSubElements(), BpmnConfCommonElementVo.class);
                if (!ObjectUtils.isEmpty(bpmnConfCommonElementVos)) {
                    for (BpmnConfCommonElementVo bpmnConfCommonElementVo : bpmnConfCommonElementVos) {
                        signUpNodeCollectionNameMap.put(bpmnConfCommonElementVo.getElementId(), bpmnConfCommonElementVo.getCollectionName());
                    }
                }
            }
        }

        return signUpNodeCollectionNameMap;
    }
    /**
     * 根据processNumber 获取当前审批节点的ElementId
     * @param processNumber
     * @return
     */
    public  String  findCurrentNodeIds(String processNumber) {
      return  service.findCurrentNodeIds(processNumber);
    }
    /**
     * do append verify info
     *
     * @param sort
     * @param elementId
     * @param activitiList
     * @param nodeApproveds
     * @param bpmVerifyInfoVos
     */
    private void doAddBpmVerifyInfoVo(Integer sort, String elementId, List<ActivityImpl> activitiList,
                                      Map<String, List<BaseIdTranStruVo>> nodeApproveds,
                                      Map<String, String> signUpNodeCollectionNameMap,
                                      List<BpmVerifyInfoVo> bpmVerifyInfoVos, Multimap<String,
            HistoricVariableInstance> variableInstanceMap,
            Long variableId) {

        //get the netxt pvm activity element
        List<PvmActivity> nextElements = activitiAdditionalInfoService.getNextElementList(processRepeatElementId(elementId), activitiList);

        if (CollectionUtils.isEmpty(nextElements)) {
            return;
        }
        List<String> empIds = new ArrayList<>();

        List<String> emplNames = Lists.newArrayList();

        for (PvmActivity nextElement : nextElements) {
            //get next node's approvers
            List<BaseIdTranStruVo> baseIdTranStruVos = nodeApproveds.get(nextElement.getId());
            if (!ObjectUtils.isEmpty(baseIdTranStruVos)) {
                for (BaseIdTranStruVo empBaseInfo : baseIdTranStruVos) {
                    String emplIdStr=empBaseInfo.getId();
                    String name = empBaseInfo.getName();
                    empIds.add(emplIdStr);
                    emplNames.add(name);
                }
            }
        }

        //then assemble them
        String verifyUserName="";
        if (!CollectionUtils.isEmpty(emplNames)) {
            verifyUserName = StringUtils.join(emplNames, ",");
        } else {

            //If can not get the approvers info,then get it from activity engine
            verifyUserName = activitiAdditionalInfoService.getVerifyUserNameFromHis(nextElements.get(0).getId(), signUpNodeCollectionNameMap, variableInstanceMap,variableId);
        }
        StringBuilder nameSb=new StringBuilder();
        StringBuilder elementIdSb=new StringBuilder();
        for (int i = 0; i < nextElements.size(); i++) {
            PvmActivity currElement = nextElements.get(i);
            if(i!=nextElements.size()-1){
                nameSb.append(currElement.getProperty("name")).append("||");
                elementIdSb.append(currElement.getId()).append(",");
            }else{
                nameSb.append(currElement.getProperty("name"));
                elementIdSb.append(currElement.getId());

            }
        }

        BpmVerifyInfoVo bpmVerifyInfoVo = BpmVerifyInfoVo.builder().elementId(elementIdSb.toString()).taskName(nameSb.toString()).verifyDesc(StringUtils.EMPTY).verifyStatus(0).verifyUserIds(empIds).verifyUserName(verifyUserName).sort(sort).build();
	    List<BpmVariableMultiplayer> multiplayers = bpmVariableMultiplayerService.lambdaQuery().eq(BpmVariableMultiplayer::getVariableId, variableId).eq(BpmVariableMultiplayer::getElementId, bpmVerifyInfoVo.getElementId()).select(BpmVariableMultiplayer::getNodeId).list();
	    if (!CollectionUtils.isEmpty(multiplayers)) {
		    List<String> nodeIds = Lists.transform(multiplayers, BpmVariableMultiplayer::getNodeId);
		    if (!CollectionUtils.isEmpty(nodeIds)) {
			    Long signTypeThreeCount = bpmnNodePersonnelConfService.lambdaQuery()
				    .eq(BpmnNodePersonnelConf::getSignType, SignTypeEnum.SIGN_TYPE_SIGN_IN_ORDER.getCode())
				    .in(BpmnNodePersonnelConf::getBpmnNodeId, nodeIds).count();
			    bpmVerifyInfoVo.setSignTypeThree(signTypeThreeCount > 0);
		    }
	    }
        //add to verify infos
        if (!ObjectUtils.isEmpty(bpmVerifyInfoVo.getVerifyUserName()) && !bpmVerifyInfoVo.getTaskName().equals("EndEvent")) {
			// 加批时，会重复增加一遍审批人，如果bpmVerifyInfoVos中已经有了与bpmVerifyInfoVo相同的verifyUserName和taskName和elementId,那就跳过add不添加
	        if (bpmVerifyInfoVos.stream().noneMatch(x ->
		            Objects.equals(bpmVerifyInfoVo.getVerifyUserName(), x.getVerifyUserName()) &&
			        Objects.equals(x.getTaskName(), bpmVerifyInfoVo.getTaskName()) &&
			        Objects.equals(x.getElementId(), bpmVerifyInfoVo.getElementId())
	        )) {
		        bpmVerifyInfoVos.add(bpmVerifyInfoVo);
			}
            sort++;
        }


        for (PvmActivity nextElement : nextElements) {
            //get next node's next node,if it still exist,then treat it recursively
            PvmActivity nextNextElement = activitiAdditionalInfoService.getNextElement(nextElement.getId(), activitiList);
            if (!ObjectUtils.isEmpty(nextNextElement)) {
                doAddBpmVerifyInfoVo(sort, nextElement.getId(), activitiList, nodeApproveds, signUpNodeCollectionNameMap, bpmVerifyInfoVos, variableInstanceMap,variableId);
            }
        }

    }

    /**
     * @param variableId
     * @return
     */
    public Map<String, List<BaseIdTranStruVo>> getNodeApproveds(Long variableId) {

        Map<String, List<BaseIdTranStruVo>> nodeApprovedsMap = Maps.newHashMap();
        List<BpmVariableSingle> variableSingles = bpmVariableSingleService.getBaseMapper().selectList(new QueryWrapper<BpmVariableSingle>().eq("variable_id", variableId));

        if (!variableSingles.isEmpty()) {
            for (BpmVariableSingle bpmVariableSingle : variableSingles) {
                nodeApprovedsMap.put(bpmVariableSingle.getElementId(), Lists.newArrayList(BaseIdTranStruVo.builder().id(bpmVariableSingle.getAssignee()).name(bpmVariableSingle.getAssigneeName()).build()));
            }
        }
        List<BpmVariableMultiplayer> bpmVariableMultiplayers = bpmVariableMultiplayerService.getBaseMapper().selectList(new QueryWrapper<BpmVariableMultiplayer>().eq("variable_id", variableId));

        if (!bpmVariableMultiplayers.isEmpty()) {
            for (BpmVariableMultiplayer bpmVariableMultiplayer : bpmVariableMultiplayers) {
                List<BpmVariableMultiplayerPersonnel> bpmVariableMultiplayerPersonnels = bpmVariableMultiplayerPersonnelService.getBaseMapper().selectList(new QueryWrapper<BpmVariableMultiplayerPersonnel>().eq("variable_multiplayer_id", bpmVariableMultiplayer.getId()));
                if (!ObjectUtils.isEmpty(bpmVariableMultiplayerPersonnels)) {
                    nodeApprovedsMap.put(bpmVariableMultiplayer.getElementId(), bpmVariableMultiplayerPersonnels.stream().map(a->BaseIdTranStruVo.builder().id(a.getAssignee()).name(a.getAssigneeName()).build()).collect(Collectors.toList()));
                }
            }
        }
        return nodeApprovedsMap;
    }

	/**
	 * 获取当前流程退回的节点key和退回类型
	 * @param processNumber 流程编号
	 * @return 返回节点key和退回类型
	 */
	private List<Pair<String, Integer>> getNodeKeyAndBackTypeByProcessNumber(String processNumber) {
		// 退回的审核记录列表
		List<BpmVerifyInfo> backNodeList = bpmVerifyInfoServiceImpl.lambdaQuery()
			.eq(BpmVerifyInfo::getProcessCode, processNumber).eq(BpmVerifyInfo::getVerifyStatus, 8)
			.list();
		if (!CollectionUtils.isEmpty(backNodeList)) {
			if (backNodeList.size() == 1) {
				return Collections.singletonList(getStringIntegerPair(backNodeList.get(0)));
			} else {
				List<Pair<String, Integer>> rs = new ArrayList<>();
				log.info("当前流程退回多次，processNumber={}, backNodeList={}", processNumber, JSON.toJSONString(backNodeList));
				for (BpmVerifyInfo bpmVerifyInfo : backNodeList) {
					rs.add(getStringIntegerPair(bpmVerifyInfo));
				}
				return rs;
			}
		}
		return null;
	}

	/**
	 * 获取单次退回的nodeKey和backType
	 */
	private Pair<String, Integer> getStringIntegerPair(BpmVerifyInfo info) {
		String taskId = info.getTaskId();
		if (StringUtils.isBlank(taskId)) {
			return null;
		}
		String procInstIdByTaskId = processApprovalMapper.findProcInstIdByTaskId(taskId);
		BpmProcessNodeSubmit bpmProcessNodeSubmit = bpmProcessNodeSubmitService.findBpmProcessNodeSubmit(procInstIdByTaskId);
		return Pair.of(bpmProcessNodeSubmit.getNodeKey(), bpmProcessNodeSubmit.getBackType());
	}

	public String processRepeatElementId(String elementId) {
		if (StringUtils.isNotBlank(elementId) && elementId.contains(",")) {
			// 分割字符串
			String[] parts = elementId.split(",");

			// 检查所有元素是否相同
			boolean allSame = true;
			for (int i = 1; i < parts.length; i++) {
				if (!parts[i].equals(parts[0])) {
					allSame = false;
					break;
				}
			}
			// 如果所有元素相同，则只保留第一个元素
			if (allSame && parts.length > 0) {
				elementId = parts[0].trim();
			}
		}
		return elementId;
	}
}
