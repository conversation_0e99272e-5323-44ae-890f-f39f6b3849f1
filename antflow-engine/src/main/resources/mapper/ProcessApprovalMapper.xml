<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.openoa.engine.bpmnconf.mapper.ProcessApprovalMapper">
    <select id="viewPcpNewlyBuildList" resultType="org.openoa.base.vo.TaskMgmtVO">
        <include refid="viewNewlyBuild"/>
        <include refid="base_param_sql"/>
        order by s.createTime desc
    </select>
    <select id="viewPcAlreadyDoneList" resultType="org.openoa.base.vo.TaskMgmtVO">
        <include refid="viewAlreadyDone"/>
        <include refid="base_param_sql"/>
        ORDER BY s.runTime DESC
    </select>
    <select id="viewPcToDoList" resultType="org.openoa.base.vo.TaskMgmtVO">
        <include refid="viewPcToDo"/>
        <include refid="base_param_sql"/>
        ORDER BY s.runTime DESC
    </select>
    <select id="allProcessList" resultType="org.openoa.base.vo.TaskMgmtVO">
        <include refid="allProcess"/>
        <include refid="base_param_sql"/>
        ORDER BY s.runTime DESC
    </select>
    <select id="isOperational" resultType="java.lang.Integer">
        select  count(1) from  bpm_process_operation  w  where  w.process_node=#{taskMgmtVO.taskName} and w.process_key=#{taskMgmtVO.processKey}  and  w.type=#{taskMgmtVO.type}
    </select>
    <select id="doneTodayProcess" resultType="java.lang.Integer">
        SELECT count(distinct h.PROC_INST_ID_)
        from ACT_HI_TASKINST h
        LEFT JOIN bpm_business_process b ON b.PROC_INST_ID_ = h.PROC_INST_ID_
        where h.TASK_DEF_KEY_ <![CDATA[ <> ]]> 'task1418018332271'
          and h.ASSIGNEE_ = #{createUserId}
          and DATE_FORMAT(h.END_TIME_, '%Y-%m-%d') = DATE_FORMAT(SYSDATE(), '%Y-%m-%d')
          AND b.is_del = 0
    </select>
    <select id="doneCreateProcess" resultType="java.lang.Integer">
        SELECT count(*)
        from ACT_HI_PROCINST p
                 LEFT JOIN bpm_business_process b ON b.PROC_INST_ID_ = p.PROC_INST_ID_
        where p.START_USER_ID_ = #{createUserId}
          and DATE_FORMAT(p.START_TIME_, '%Y-%m-%d') = DATE_FORMAT(SYSDATE(), '%Y-%m-%d')
          AND b.is_del = 0
    </select>
    <sql id="viewNewlyBuild">
        select *
        from (
                 SELECT h.PROC_INST_ID_    AS processInstanceId,
                        h.PROC_DEF_ID_     AS processId,
                        h.START_USER_ID_   AS userId,
                        u.user_name        AS createUser,
                        h.START_TIME_      AS createTime,
                        h.START_TIME_      AS runTime,
                        b.BUSINESS_ID      AS businessId,
                        b.BUSINESS_NUMBER  AS processNumber,
                        b.description      AS description,
                        b.process_state    AS processState,
                        b.PROCESSINESS_KEY AS processKey,
                        b.process_state    AS taskStype,
                        b.process_digest   as processDigest,
                        b.UPDATE_TIME      as updateTime,
                        b.submit_user_id   as submitUserId,
                        b.submit_user_name as submitUserName,
                        tc.id      AS templateId,
                        tc.template_group_id AS templateGroupId,
                        tc.bpmn_name       AS templateName,
                        tc.template_group_name  AS templateGroupName
                 FROM ACT_HI_PROCINST h
                          LEFT JOIN bpm_business_process b ON b.PROC_INST_ID_ = h.PROC_INST_ID_
                          LEFT JOIN t_user u ON u.id = h.START_USER_ID_
                          LEFT JOIN t_bpmn_conf tc ON tc.id = b.template_id
                 where h.START_USER_ID_ = #{taskMgmtVO.applyUser}
                   and b.is_del = 0
             ) s
        where 1 = 1
        <if test="taskMgmtVO.startCreateTime != null">
            and s.createTime <![CDATA[ >= ]]> #{taskMgmtVO.startCreateTime}
        </if>
        <if test="taskMgmtVO.endCreateTime != null">
            and s.createTime <![CDATA[ <= ]]> #{taskMgmtVO.endCreateTime}
        </if>
    </sql>
    <!--query conditions-->
    <sql id="base_param_sql">
        <if test="taskMgmtVO.search!=null and taskMgmtVO.search!=''">
            AND (
            s.description LIKE CONCAT('%', #{taskMgmtVO.search},'%')
            OR s.processNumber LIKE CONCAT('%', #{taskMgmtVO.search},'%')
            )
        </if>
        <if test="taskMgmtVO.applyUserId!=null">
            and s.userId = #{taskMgmtVO.applyUserId}
        </if>
        <if test="taskMgmtVO.processName!=null and  taskMgmtVO.processName!=''">
            and s.processKey like CONCAT('%', #{taskMgmtVO.processName},'%')
        </if>
        <if test="taskMgmtVO.description!=null and  taskMgmtVO.description!=''">
            and s.description like CONCAT('%', #{taskMgmtVO.description},'%')
        </if>
        <if test="taskMgmtVO.processNumber!=null and  taskMgmtVO.processNumber!=''">
            and s.processNumber like CONCAT('%', #{taskMgmtVO.processNumber},'%')
        </if>
        <if test="taskMgmtVO.businessId!=null and  taskMgmtVO.businessId!=''">
            and s.businessId like CONCAT('%', #{taskMgmtVO.businessId},'%')
        </if>
        <if test="taskMgmtVO.processState!=null and  taskMgmtVO.processState!=''">
            and s.taskStype =#{taskMgmtVO.processState}
        </if>

        <!--start and end time-->
        <if test="taskMgmtVO.startTime!='' and taskMgmtVO.startTime!=null">
            <if test="taskMgmtVO.endTime!='' and taskMgmtVO.endTime!=null">
                and date_format(s.runTime,'%Y-%m-%d') between #{taskMgmtVO.startTime} and #{taskMgmtVO.endTime}
            </if>
        </if>
        <if test="taskMgmtVO.processKeyList != null and taskMgmtVO.processKeyList.size() > 0">
            and s.processKey in
            <foreach collection="taskMgmtVO.processKeyList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="taskMgmtVO.processNumbers != null and taskMgmtVO.processNumbers.size() > 0">
            and s.processNumber not in
            <foreach collection="taskMgmtVO.processNumbers" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="taskMgmtVO.versionProcessKeys != null and taskMgmtVO.versionProcessKeys.size > 0">
            AND s.processKey IN
            <foreach collection="taskMgmtVO.versionProcessKeys" item="versionProcessKey" open="(" separator="," close=")">
                #{versionProcessKey}
            </foreach>
        </if>
        <if test="taskMgmtVO.processDigest != null and taskMgmtVO.processDigest != ''">
            AND s.processDigest LIKE CONCAT('%', #{taskMgmtVO.processDigest},'%')
        </if>
        <if test="taskMgmtVO.templateName != null and taskMgmtVO.templateName!=''">
            and s.templateName like concat('%', #{taskMgmtVO.templateName}, '%')
        </if>
        <if test="taskMgmtVO.subOrgIds != null and taskMgmtVO.subOrgIds.size() > 0">
            and s.orgId in
            <foreach collection="taskMgmtVO.subOrgIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </sql>
    <!--already done prcoess-->
    <sql id="viewAlreadyDone">
        select *
        from (
                 SELECT t.PROC_INST_ID_    AS processInstanceId,
                        b.PROCESSINESS_KEY AS processKey,
                        h.START_USER_ID_   AS userId,
                        h.START_TIME_      AS createTime,
                        b.UPDATE_TIME      AS updateTime,
                        b.BUSINESS_ID      AS businessId,
                        b.description      AS description,
                        b.process_state    AS taskStype,
                        b.BUSINESS_NUMBER  AS processNumber,
                        t.END_TIME_        AS runTime,
                        b.process_state    AS processState,
                        u.user_name        as createUser,
                        b.process_digest   as processDigest,
                        b.APPROVE_CODE     AS approveCode,
                        tc.id              AS templateId,
                        tc.template_group_id AS templateGroupId,
                        tc.bpmn_name      AS templateName,
                        tc.template_group_name  AS templateGroupName
                       <!-- ,ROW_NUMBER() over(partition BY b.business_id ORDER BY BUSINESS_NUMBER, t.END_TIME_ DESC) AS rn-->
                 FROM ACT_HI_TASKINST t
                          LEFT JOIN ACT_HI_PROCINST h ON h.PROC_INST_ID_ = t.PROC_INST_ID_
                          LEFT JOIN bpm_business_process b ON b.PROC_INST_ID_ = h.PROC_INST_ID_
                          LEFT JOIN t_bpmn_conf tc ON tc.id = b.template_id
                          LEFT JOIN t_user u ON u.id = h.START_USER_ID_
                 where t.ASSIGNEE_ = #{taskMgmtVO.applyUser}
                   and t.END_TIME_ IS NOT NULL
                   and t.TASK_DEF_KEY_ <![CDATA[ <> ]]> 'task1418018332271'
                   and b.is_del = 0
             ) s
                where s.runTime = (
                    select max(t2.END_TIME_)
                    from ACT_HI_TASKINST t2
                    LEFT JOIN ACT_HI_PROCINST h2 ON h2.PROC_INST_ID_ = t2.PROC_INST_ID_
                    LEFT JOIN bpm_business_process b2 ON b2.PROC_INST_ID_ = h2.PROC_INST_ID_
                    where b2.BUSINESS_NUMBER = s.processNumber
                    and t2.ASSIGNEE_ = #{taskMgmtVO.applyUser}
                    and t2.END_TIME_ IS NOT NULL
                    and t2.TASK_DEF_KEY_ <![CDATA[ <> ]]> 'task1418018332271'
                    and b2.is_del = 0
                )
      <!--  and s.rn = 1-->
    </sql>
    <!--query to do list for the pc-->
    <sql id="viewPcToDo">
        SELECT *
        FROM (
                 SELECT t.PROC_INST_ID_    AS processInstanceId,
                        b.PROCESSINESS_KEY AS processKey,
                        b.create_user      AS userId,
                        ns.`back_type`     AS backType,
                        b.user_name        AS createUser,
                        b.CREATE_TIME      AS createTime,
                        b.BUSINESS_ID      AS businessId,
                        b.description      AS description,
                        b.process_state    AS taskStype,
                        t.NAME_            AS nodeName,
                        b.BUSINESS_NUMBER  AS processNumber,
                        t.ID_              AS taskId,
                        t.CREATE_TIME_     AS runTime,
                        t.TASK_DEF_KEY_    AS  taskName,
                        b.process_state    AS processState,
                        b.process_digest   as processDigest,
                        b.UPDATE_TIME      AS updateTime,
                        b.template_id      AS templateId,
                        b.template_group_id AS templateGroupId,
                        tc.bpmn_name       AS templateName,
                        tc.template_group_name  AS templateGroupName
                 FROM ACT_RU_TASK t
                          LEFT JOIN bpm_business_process b ON b.PROC_INST_ID_ = t.PROC_INST_ID_
                          LEFT JOIN t_bpmn_conf tc ON tc.id = b.template_id
                          LEFT JOIN bpm_process_node_submit ns ON t.PROC_INST_ID_ = ns.processInstance_Id
                 where t.ASSIGNEE_ = #{taskMgmtVO.applyUser}
                   and b.is_del = 0
             ) s
        where 1 = 1
    </sql>
    <sql id="allProcess">
        select *
        from (
                 SELECT
                        b.PROCESSINESS_KEY AS processKey,
                        h.START_USER_ID_   AS userId,
                        b.BUSINESS_ID      AS businessId,
                        b.description      AS description,
                        b.process_state    AS taskStype,
                        b.BUSINESS_NUMBER  AS processNumber,
                        h.START_TIME_      AS runTime,
                        h.START_TIME_      AS createTime,
                        b.process_state    AS processState,
                        b.process_digest   as processDigest,
                        b.update_time      AS updateTime,
                        tc.id              AS templateId,
                        tc.bpmn_name       AS templateName,
                        tc.template_group_id AS templateGroupId,
                        tc.template_group_name AS templateGroupName,
                        u.user_name        AS createUser,
                        u.org_id           AS orgId
                 FROM ACT_HI_PROCINST h
                          LEFT JOIN t_user u ON u.id = h.START_USER_ID_
                          LEFT JOIN bpm_business_process b ON b.ENTRY_ID = h.BUSINESS_KEY_
                          LEFT JOIN  t_bpmn_conf tc ON tc.form_code = b.PROCESSINESS_KEY
                 where b.is_del = 0
             ) s
        where 1 = 1
    </sql>

    <!--get forwarded process-->
    <sql id="viewForward">
        SELECT * FROM (
        SELECT
        b.PROCESSINESS_KEY AS processKey,
        b.create_user AS userId,
        b.CREATE_TIME AS createTime,
        b.BUSINESS_ID AS businessId,
        b.description AS description,
        b.process_state AS taskStype,
        b.BUSINESS_NUMBER AS processNumber,
        f.create_time  AS runTime,
        b.process_state AS processState,
        b.process_digest as processDigest,
        u.user_name as createUser,
        b.APPROVE_CODE     AS approveCode,
        tc.id              AS templateId,
        tc.template_group_id AS templateGroupId,
        tc.bpmn_name      AS templateName,
        tc.template_group_name  AS templateGroupName
        FROM bpm_business_process b
        LEFT JOIN (
            SELECT forward_user_id, process_number, min(create_time) as create_time
            FROM bpm_process_forward
            WHERE is_del = 0
            group by forward_user_id, process_number
        )  f ON b.BUSINESS_NUMBER = f.process_number
        LEFT JOIN t_bpmn_conf tc ON tc.id = b.template_id
        LEFT JOIN t_user u ON u.id = b.create_user
        WHERE f.forward_user_id = #{taskMgmtVO.applyUser}
          AND b.is_del = 0
          AND b.BUSINESS_ID IS NOT NULL
        ORDER BY f.create_time DESC
        ) s WHERE  1=1
    </sql>
    <select id="viewPcForwardList" resultType="org.openoa.base.vo.TaskMgmtVO">
        <include refid="viewForward"/>
        <include refid="base_param_sql"/>

    </select>
    <select id="viewPcProcessList" resultType="org.openoa.base.vo.TaskMgmtVO">
        select * from (
        SELECT
        h.PROC_INST_ID_ AS processInstanceId,
        h.PROC_DEF_ID_ AS processId,
        h.START_USER_ID_ AS userId,
        b.BUSINESS_ID AS businessId,
        b.BUSINESS_NUMBER AS processNumber,
        b.description AS description,
        b.process_state AS processState,
        b.PROCESSINESS_KEY AS processKey,
        b.process_state AS taskStype,
        h.START_TIME_ as runTime,
        b.process_digest as processDigest,
        b.VERSION as appVersion,
        b.approval_user_id AS approvalUserId
        FROM
        ACT_HI_PROCINST h
        LEFT JOIN bpm_business_process b ON b.ENTRY_ID = h.BUSINESS_KEY_
        where b.is_del=0
        order by h.START_TIME_ desc
        ) s where 1=1
        <include refid="base_param_sql"/>
        ORDER BY s.runTime desc
    </select>
    <select id="queryProcessBaseInfo" resultType="org.openoa.base.vo.PdfBaseInfoVO">
        SELECT h.PROC_INST_ID_    AS processInstanceId,
               bc.id              AS bpmnConfId,
               bc.bpmn_name       AS bpmnName,
               u.id               AS empId,
               u.user_name        AS empName,
               h.START_TIME_      AS applyTime,
               h.END_TIME_        AS endTime,
               b.process_state    AS approvalStatus,
               b.UPDATE_TIME      AS updateTime,
               o.org_name         AS orgName,
               o.org_id         AS orgId,
               u.ehr_source       AS ehrSource
        FROM ACT_HI_PROCINST h
            LEFT JOIN bpm_business_process b ON b.PROC_INST_ID_ = h.PROC_INST_ID_
            LEFT JOIN t_bpmn_conf bc ON bc.id = b.template_id
            LEFT JOIN t_user u ON u.id = h.START_USER_ID_
            LEFT JOIN sync_ehr_org_info o ON o.org_id = u.org_id AND o.ehr_source = u.ehr_source
        WHERE b.BUSINESS_NUMBER = #{processNumber} AND b.is_del = 0
    </select>

    <select id="findProcInstIdByTaskId" resultType="java.lang.String">
        SELECT `PROC_INST_ID_` FROM `act_hi_actinst` WHERE `TASK_ID_` = #{taskId} limit 1
    </select>

    <select id="todoCount" resultType="java.lang.Integer">
        SELECT count(1)
        FROM ACT_RU_TASK t LEFT JOIN bpm_business_process b ON b.PROC_INST_ID_ = t.PROC_INST_ID_
        where t.ASSIGNEE_ = #{userId} and b.is_del = 0
    </select>


</mapper>