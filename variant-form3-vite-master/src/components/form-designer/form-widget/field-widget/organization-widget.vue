<template>
  <form-item-wrapper :designer="designer" :field="field" :rules="rules" :design-state="designState"
                     :parent-widget="parentWidget" :parent-list="parentList" :index-of-parent-list="indexOfParentList"
                     :sub-form-row-index="subFormRowIndex" :sub-form-col-index="subFormColIndex" :sub-form-row-id="subFormRowId">
    <div class="full-width-input">
      <el-select
        v-model="ehrSource"
        @change="handleCompanyChange"
        placeholder="请选择公司主体"
        :size="widgetSize"
        :disabled="field.options.disabled"
        style="width: 180px"
      >
        <el-option
          v-for="item in companyOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-cascader ref="fieldEditor" :options="field.options.optionItems" v-model="fieldModel"
                  :disabled="field.options.disabled"
                  :size="widgetSize"
                  :clearable="field.options.clearable"
                  :filterable="field.options.filterable"
                  :placeholder="field.options.placeholder || i18nt('render.hint.selectPlaceholder')"
                  :collapse-tags="true"
                  :collapse-tags-tooltip="true"
                  :show-all-levels="showFullPath"
                  :props="{ multiple: field.options.multiple, expandTrigger: 'hover', emitPath: false }"
                  @focus="handleFocusCustomEvent" @blur="handleBlurCustomEvent"
                  @change="handleChangeEvent">
      </el-cascader>
    </div>
    <div class="data-selected">
      <el-tag v-for="(tag, index) in allFieldModel" :key="index" closable @close="deleteTag(tag)" type="success">
        {{ `${ehrSourceMap[String(tag.ehrSource)]}：${tag.name}` }}
      </el-tag>
    </div>
  </form-item-wrapper>
</template>

<script>
  import axios from 'axios'
  import FormItemWrapper from './form-item-wrapper'
  import emitter from '@/utils/emitter'
  import i18n, {translate} from "@/utils/i18n";
  import fieldMixin from "@/components/form-designer/form-widget/field-widget/fieldMixin";
  import { EHR_SOURCE, EHR_SOURCE_MAP } from '@/utils/constants'

  export default {
    name: "organization-widget",
    componentName: 'FieldWidget',  //必须固定为FieldWidget，用于接收父级组件的broadcast事件
    mixins: [emitter, fieldMixin, i18n],
    props: {
      field: Object,
      parentWidget: Object,
      parentList: Array,
      indexOfParentList: Number,
      designer: Object,

      designState: {
        type: Boolean,
        default: false
      },

      subFormRowIndex: { /* 子表单组件行索引，从0开始计数 */
        type: Number,
        default: -1
      },
      subFormColIndex: { /* 子表单组件列索引，从0开始计数 */
        type: Number,
        default: -1
      },
      subFormRowId: { /* 子表单组件行Id，唯一id且不可变 */
        type: String,
        default: ''
      },

    },
    components: {
      FormItemWrapper,
    },
    data() {
      return {
        oldFieldValue: null, //field组件change之前的值
        fieldModel: null,
        allFieldModel: [],
        rules: [],
        ehrSource: null,
        companyOptions: EHR_SOURCE,
        ehrSourceMap: EHR_SOURCE_MAP,
        orgMemberInfo: {} // 所有公司成员信息
      }
    },
    computed: {
      showFullPath() {
        return (this.field.options.showAllLevels === undefined) || !!this.field.options.showAllLevels
      },

    },
    beforeCreate() {
      /* 这里不能访问方法和属性！！ */
    },

    created() {
      /* 注意：子组件mounted在父组件created之后、父组件mounted之前触发，故子组件mounted需要用到的prop
         需要在父组件created中初始化！！ */
      this.field.options.optionItems = []
      this.fieldModel = null
      this.initOptionItems()
      this.initFieldModel()
      this.registerToRefList()
      this.initEventHandler()
      this.buildFieldRules()

      this.handleOnCreated()
    },

    mounted() {
      this.handleOnMounted()
    },

    beforeUnmount() {
      this.unregisterFromRefList()
    },

    methods: {
      // 获取不同公司下的成员数据
      handleCompanyChange(value) {
        if(this.field.options.multiple) { // 多选时fieldModel是数组
          this.fieldModel = this.allFieldModel.filter(item => item.ehrSource == value).map(item => item.id)
        } else { // 单选时fieldModel是字符串
          this.fieldModel = this.allFieldModel.filter(item => item.ehrSource == value)[0]?.id || null
        }
        if(!value) {
          this.field.options.optionItems = []
          return
        } else {
          if(this.orgMemberInfo[value]?.length) {
            this.field.options.optionItems = this.orgMemberInfo[value]
            return
          }
          axios.post('https://scrm-test.ceboss.cn/antflow/user/getOrgTree', { ehrSource: value, type: 2}).then(res => {
            if(res?.status === 200 && res?.data?.data) {
              this.orgMemberInfo[value] = this.field.options.optionItems = res.data.data.children || [] 
            }
          }).catch(error => {
            this.$message.error('成员数据获取失败，请稍后再试')
          })
        }
      },

      // 删除成员
      deleteTag(tag) {
        this.allFieldModel = this.allFieldModel.filter(item => item.id != tag.id)

        if(this.field.options.multiple) { // 多选时fieldModel是数组
          this.fieldModel = this.fieldModel.filter(item => item != tag.id)
        } else { // 单选时fieldModel是字符串
          this.fieldModel = null
        }
      }
    }
  }
</script>

<style lang="scss" scoped>
  .full-width-input {
    width: 100% !important;
    display: flex;
    align-items: center;
    gap: 10px;

    :deep(.el-cascader) {
      width: 100% !important;
    }
  }
  .data-selected {
    max-height: 100px;
    overflow: auto;
    :deep(.el-tag) {
      margin-right: 10px;
    }
  }
</style>
