import { createWebHashHistory, createRouter } from 'vue-router'
/* Layout */
import Layout from '@/layout'

// 公共路由
export const constantRoutes = [
  {
    path: '/login',
    component: () => import('@/views/login'),
    hidden: true
  }, 
  {
    path: "/:pathMatch(.*)*",
    component: () => import('@/views/error/404'),
    hidden: true
  },
  {
    path: '/401',
    component: () => import('@/views/error/401'),
    hidden: true
  },
  {
    path: '',
    component: Layout,
    redirect: '/approveDetail',
    children: [
      {
        path: '/approveDetail',
        component: () => import('@/views/approve-detail/index'),
        name: 'Index',
        meta: { title: '审批详情' }
      }
    ]
  }
]

const router = createRouter({   
  history: createWebHashHistory(import.meta.env.BASE_URL),//createWebHistory(), //
  routes: constantRoutes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  },
});

export default router;
