(function() {
  // 定义环境映射
  const ENV_LIST = ['test', 'pre', 'dev', 'localhost']
  
  // 获取当前环境
  const domainPrefix = window.location.hostname.split('-')[0];
  const env = ENV_LIST.includes(domainPrefix) ? domainPrefix : 'prod';

  let APIURL = `https://scrm${env === 'prod' ? '' : '-test'}.ceboss.cn`
  // 设置全局变量
  window.__ce = {
    ENV: env,
    APIURL: APIURL, // 后端接口地址
    BASEURL: `${APIURL}/antflow`, // 后端接口基础地址
    WEBURL: `https://${env === 'prod' ? '' : 'test-'}cescrm.ceboss.cn`, // 前端展示地址
  };
})();