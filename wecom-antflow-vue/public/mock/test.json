{"annotation": "", "approvalStandard": 2, "approveRemindVo": null, "batchStatus": 0, "buttons": {"approvalPage": [3, 4], "startPage": [1], "viewPage": []}, "conditionsUrl": "", "confId": 155, "createTime": "2024-12-13 21:08:08", "createUser": "张三", "deduplicationExclude": false, "elementId": "", "formCode": "OVER_LF", "fromNodes": [], "id": 423, "isDeduplication": 0, "isDel": 0, "isLowCodeFlow": 1, "isOutSideProcess": 0, "isSignUp": 0, "lfFieldControlVOs": [], "nodeDisplayName": "", "nodeFrom": "NE4FML", "nodeFroms": "", "nodeId": "7GAFML", "nodeName": "审核人", "nodeProperty": 5, "nodePropertyName": "指定人员", "nodeTo": [], "nodeType": 4, "orderedNodeType": 0, "params": null, "prevId": [], "property": {"afterSignUpWay": 0, "assignLevelGrade": 0, "assignLevelType": 0, "conditionList": [], "conditionsConf": null, "configurationTableType": 0, "emplIds": ["7"], "emplList": [{"id": "7", "name": "李九"}], "functionId": 0, "functionName": "", "hrbpConfType": 0, "isDefault": 0, "isMultiPeople": 0, "loopEndGrade": 0, "loopEndPersonList": [], "loopEndPersonObjList": [], "loopEndType": 0, "loopNumberPlies": 0, "nodeMark": "", "noparticipatingStaffIds": [], "noparticipatingStaffs": [], "roleIds": [], "roleList": [], "signType": 1, "signUpType": 0, "sort": 0, "tableFieldType": 0}, "remark": "", "templateVos": [], "updateTime": "2024-12-13 21:08:08", "updateUser": "", "signType": 1, "setType": 5, "nodeApproveList": [{"type": 5, "targetId": 7, "name": "李九"}], "conditionNodes": []}