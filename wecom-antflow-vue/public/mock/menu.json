{"msg": "操作成功", "code": 200, "data": [{"name": "flowTask", "path": "/flowTask", "hidden": false, "redirect": "noRedirect", "component": "Layout", "alwaysShow": true, "meta": {"title": "任务中心", "icon": "example", "noCache": false, "link": null}, "children": [{"name": "MyTask", "path": "mytask", "hidden": false, "component": "flowTask/mytask/index", "meta": {"title": "我的发起", "icon": "", "noCache": false, "link": null}}, {"name": "PenddingTask", "path": "pendding", "hidden": false, "component": "flowTask/pendding/index", "meta": {"title": "待办任务", "icon": "", "noCache": false, "link": null}}, {"name": "ApprovedTask", "path": "approved", "hidden": false, "component": "flowTask/approved/index", "meta": {"title": "已办任务", "icon": "", "noCache": false, "link": null}}, {"name": "CopyToMeTask", "path": "CopyToMe", "hidden": false, "component": "flowTask/copyToMe/index", "meta": {"title": "抄送到我", "icon": "", "noCache": false, "link": null}}, {"name": "approve", "path": "pendding/approve", "hidden": true, "component": "flowTask/pendding/approve", "meta": {"title": "流程审批", "icon": "", "noCache": true, "link": null}}]}, {"name": "workflow", "path": "/workflow", "hidden": false, "redirect": "noRedirect", "component": "Layout", "alwaysShow": true, "meta": {"title": "模版管理", "icon": "system", "noCache": false, "link": null}, "children": [{"name": "templateGroup", "path": "group", "hidden": false, "component": "workflow/group/index", "meta": {"title": "模版分组", "icon": "", "noCache": false, "link": null}}, {"name": "templateDesign", "path": "design", "hidden": false, "component": "workflow/design/index", "meta": {"title": "模版设计", "icon": "", "noCache": false, "link": null}}, {"name": "applyRecord", "path": "applyRecord", "hidden": false, "component": "workflow/applyRecord/index", "meta": {"title": "申请记录", "icon": "", "noCache": false, "link": null}}, {"name": "lf-FlowDesign", "path": "lf-design", "hidden": true, "component": "workflow/design/lf", "meta": {"title": "低代码表单流程设计", "icon": "", "noCache": false, "link": null}}, {"name": "diy-FlowDesign", "path": "diy-design", "hidden": true, "component": "workflow/design/diy", "meta": {"title": "自定义表单流程设计", "icon": "", "noCache": false, "link": null}}, {"name": "flowPreview", "path": "preview", "hidden": true, "component": "workflow/preview/index", "meta": {"title": "流程模板预览", "icon": "", "noCache": false, "link": null}}, {"name": "startFlow", "path": "/startFlow/index", "hidden": true, "component": "startFlow/index", "meta": {"title": "发起申请流程", "icon": "", "noCache": true, "link": null}}, {"name": "startOutside", "path": "/startOutside/index", "hidden": true, "component": "startOutside/index", "meta": {"title": "业务方表单发起测试", "icon": "", "noCache": false, "link": null}}, {"name": "version", "path": "flow-version", "hidden": true, "component": "workflow/config/version", "meta": {"title": "版本管理", "icon": "", "noCache": true, "link": null}}]}, {"name": "label", "path": "/label", "hidden": false, "redirect": "noRedirect", "component": "Layout", "alwaysShow": true, "meta": {"title": "用户标签管理", "icon": "clipboard", "noCache": false, "link": null}, "children": [{"name": "userLabel", "path": "userLabel", "hidden": false, "component": "label/userLabel/index", "meta": {"title": "用户标签", "icon": "", "noCache": false, "link": null}}]}]}