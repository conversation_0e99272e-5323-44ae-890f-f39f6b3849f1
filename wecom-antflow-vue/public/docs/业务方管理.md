# 业务方管理简介

* 业务方：也可称为三方接入，即第三方的系统或项目，如OA系统，HR系统，CRM系统等等。

* 目标：工作流项目单独部署，作为审批流转的一个微服务，其他项目通过api形式接入审批流程，发起，审批等操作
 
* 菜单最新调整如下：
```
├── 业务方管理
   ├── 项目管理
   ├── 业务管理 
   └── 流程设计
```
 
## 项目管理

* 项目管理 : 可以是以 一个项目、公司或组织 添加一条记录。下面文档以**以项目为例: OA管理系统等等**

## 业务管理

* 业务管理：一个项目可以有多个不同的业务表单，业务表单关联到项目，**例如：员工入职流程，员工离职流程，请假流程，加班流程等等** 

* 设置人员：业务表单需要配置【审批人，审批角色】接口，用于流程设计时审批人节点选择。**注：相关人员对应这个业务表单的审批人**

例如：http://117.72.70.166/user/getUser 目前仅仅支持get格式，后期优化支持post格式

* 设置条件：业务表单需要配置【审批条件】，用于流程设计时条件节点选择。**注：条件对应这个业务表单的判断条件**

#### 条件描述
* 比如 amount > 1000 
* 条件模板ID： amountgt1000
* 条件模板名称：金额大于1000
* 在业务表单提交，接入审批流时候，如果实际业务amount字段值大于1000，则templateMarks :['amountgt1000'] 否则 templateMark :''

**条件节点后期优化，目前一个表单仅支持配置一个条件后期开发多个条件。**

#### 三方业务表单发起
OA管理系统，通过api 形式发起表单，格式如下：
```
接口名称 /outSide/processSubmit

参数：
{
    formCode:"adbgxx",       //string  必填   表单formcode   
    templateMarks:"['gt100','gt200']", //array   不必填 条件id  满足条件：填写条件Id,不满足则空着  
    formDataPc:"",           //string  不必填 用于审批时预览表单数据 
    userId:"1",              //int     必填   发起人id     
} 
//formDataPc示例 [{\"label\":\"人员姓名\",\"value\":\"张三5\"},{\"label\":\"年龄\",\"value\":\"55\"},{\"label\":\"备注\",\"value\":\"外部系统业务表单接入测试55\"}] 

``` 
## 流程设计

流程设计：为每个业务表单设计一个审批流程

与传统流程相比，审批节点暂时不考虑 字段权限控制这个功能。
 