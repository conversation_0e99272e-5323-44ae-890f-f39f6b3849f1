# 前情需知

### 基础信息模块

- 1、项目中登录，登出，菜单，个人信息采用静态json文件模拟数据,json文件位于public/mock/文件夹下
- 2、模拟请求api文件位置 src/api/mock.js
- 3、登录功能是模拟登录，直接选择人员无需更改密码
- 4、如果需要对接自己项目的后端api，简单修改相关接口即可

### 工作流模块
- 1、工作流相关后端api是请求的我们部署的demo地址http://117.72.70.166:7001/doc.html
- 2、工作流请求api文件地址src/api/workflow.js
- 3、如果需要对接自己项目的后端api，需要下载后端源码https://gitee.com/tylerzhou/Antflow.git，进行部署
- 4、部署后的后端地址，替换掉src/api/workflow.js的 baseUrl = "http://117.72.70.166:7001";即可

# 前端运行

### 1、克隆项目代码到本地

```
git clone https://gitee.com/tylerzhou/Antflow.git 
```

### 2、进入项目目录

```
cd Antflow/antflow-vue
```

### 3、安装依赖
```
npm install --registry=https://registry.npmmirror.com
```
 
#### 建议不要用直接使用 cnpm 安装，会有各种诡异的 bug，可以通过重新指定 registry 来解决 npm 安装速度慢的问题。 
 
### 4\本地开发 启动项目

```
npm run dev
```


### 5、打开浏览器

输入：(http://localhost:80 (opens new window)) 默认账户/密码 admin/admin123）
若能正确展示登录页面，并能成功登录，菜单及页面展示正常，则表明环境搭建成功

建议使用Git克隆，因为克隆的方式可以和最新代码随时保持更新同步。使用Git命令克隆

```
git clone https://gitee.com/tylerzhou/Antflow.git 
```