# 前端部署

当项目开发完毕，只需要运行一行命令就可以打包你的应用

### 打包正式环境
```
npm run build:prod
```
# 打包预发布环境
```
npm run build:stage
```


1、构建打包成功之后，会在根目录生成 dist 文件夹，里面就是构建打包好的文件，通常是 ***.js 、***.css、index.html 等静态文件。

2、通常情况下 dist 文件夹的静态文件发布到你的 nginx 或者静态服务器即可，其中的 index.html 是后台服务的入口页面。

outputDir 提示

3、如果需要自定义构建，比如指定 dist 目录等，则需要通过 config (opens new window)的 outputDir 进行配置。

4、publicPath 提示

部署时改变页面js 和 css 静态引入路径 ,只需修改 vue.config.js 文件资源路径即可。

```
publicPath: './' //请根据自己路径来配置更改
export default new Router({
  mode: 'hash', // hash模式
})
```
### 环境变量
所有测试环境或者正式环境变量的配置都在 
```
.env.development (opens new window)等 
.env.xxxx文件中。
```

它们都会通过 webpack.DefinePlugin 插件注入到全局。

环境变量必须以VUE_APP_为开头。如:VUE_APP_API、VUE_APP_TITLE

你在代码中可以通过如下方式获取:

```
console.log(process.env.VUE_APP_xxxx)
```

扩展阅读：《Vue CLI - 环境变量和模式》(opens new window)

### 注意

环境配置修改后，需要重新运行才会生效