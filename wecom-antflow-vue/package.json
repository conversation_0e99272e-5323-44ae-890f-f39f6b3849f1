{"name": "wecom-antflow-vue", "version": "8.5.0", "description": "企微审批管理系统", "author": "admin", "license": "MIT", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "repository": {"type": "git", "url": "***.git"}, "dependencies": {"@vueup/vue-quill": "1.2.0", "@vueuse/core": "10.11.0", "axios": "0.28.1", "js-cookie": "3.0.5", "jsencrypt": "3.3.2", "nprogress": "0.2.0", "pinia": "2.1.7", "vant": "^4.9.19", "vue": "3.4.31", "vue-router": "4.4.0"}, "devDependencies": {"@vant/auto-import-resolver": "^1.3.0", "@vitejs/plugin-vue": "5.0.5", "sass": "1.77.5", "unplugin-auto-import": "^0.17.6", "unplugin-vue-components": "^28.5.0", "unplugin-vue-setup-extend-plus": "1.0.1", "vite": "5.3.2", "vite-plugin-compression": "0.5.1", "vite-plugin-svg-icons": "2.0.1"}}