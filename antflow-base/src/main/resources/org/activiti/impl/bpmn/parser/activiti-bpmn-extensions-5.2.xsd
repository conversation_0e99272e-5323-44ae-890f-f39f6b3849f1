<?xml version="1.0" encoding="UTF-8"?>
<schema
  xmlns="http://www.w3.org/2001/XMLSchema"
  targetNamespace="http://activiti.org/bpmn"
  xmlns:tns="http://activiti.org/bpmn"
  elementFormDefault="qualified">
  
  <!-- TODO
  
    
       formKey

       formProperty id="" type variable expression writable required
                    activiti:value
   -->

  <annotation>
    <documentation>
      This XML Schema defines and documents BPMN 2.0 extension elements and
      attributes introduced by Activiti.
    </documentation>
  </annotation>
  
  <attribute name="initiator" type="string">
    <annotation>
      <documentation>
        Attribute on a start event.
        Denotes a process variable in which the process initiator set in the 
        identityService.setAuthenticatedUserId(userId) is captured.
      </documentation>
    </annotation>
  </attribute>
  
  <attribute name="history">
    <annotation>
      <documentation>
        Attribute on the process element. 
        Allows to set the history level for this specific process definition
        differently from the history level set in the process engine configuration.
      </documentation>
    </annotation>
    <simpleType>
      <restriction base="string">
        <enumeration value="none" />
        <enumeration value="activity" />
        <enumeration value="audit" />
        <enumeration value="full" />
      </restriction>
    </simpleType>
  </attribute>
  
  <attribute name="formKey" type="string">
    <annotation>
      <documentation>
        Attribute used on a startEvent or a userTask. 
        The value can be anything. The default form support in Activiti
        assumes that this is a reference to a form html file insed the deployment
        of the process definition. But this key can also be something completely different,
        in case of external form resolving.
      </documentation>
    </annotation>
  </attribute>
  
  <attribute name="formHandlerClass">
    <annotation>
      <documentation>
        Attribute on a startEvent or userTask.
        Allows to specify a custom class that will be called during the parsing 
        of the form information. Thus way, it is possible to use custom forms and form handling.
        This class must implement the 
        org.activiti.engine.inpl.form.FormHamdler/StartFormHandler/taskFormHandler interface
        (specific interface depending on the activity). 
      </documentation>
    </annotation>
    <simpleType>
      <restriction base="string">
        <!-- regular expression taken from http://regexlib.com/REDetails.aspx?regexp_id=2821 -->
        <pattern value="([a-z]{2,3}(\.[a-zA-Z][a-zA-Z_$0-9]*)*)\.([A-Z][a-zA-Z_$0-9]*)"/>
      </restriction>
    </simpleType>
  </attribute>
  
  <element name="formProperty">
    <annotation>
      <documentation>
        Subelement of the extensionsElement of activities that support forms.
        Allows to specifies properties (!= process variables) for a form. See documentation chapter on
        form properties.
      </documentation>
    </annotation>
    <complexType>
      <!-- TODO: activiti:value -->
      <attribute name="id" use="required" type="string">
        <annotation>
          <documentation>
            The key used to submit the property through the API.
          </documentation>
        </annotation>
      </attribute>
      <attribute name="name" type="string">
        <annotation>
          <documentation>
            The display label of the property.
          </documentation>
        </annotation>
      </attribute>
      <attribute name="type" type="string">
        <annotation>
          <documentation>
            The type of the property (see documentation for supported types). 
            Default is 'string'.
          </documentation>
        </annotation>
      </attribute>
      <attribute name="readable" type="string">
        <annotation>
          <documentation>
            Specifies if the property can be read and displayed in the form.
          </documentation>
        </annotation>
      </attribute>
      <attribute name="writable" type="string">
        <annotation>
          <documentation>
            Specifies if the property is expected when the form is submitted.
          </documentation>
        </annotation>
      </attribute>
      <attribute name="required" type="string">
        <annotation>
          <documentation>
            Specifies if the property is a required field.
          </documentation>
        </annotation>
      </attribute>
      <attribute name="variable" type="string">
        <annotation>
          <documentation>
            Specifies the process variable on which the variable is mapped.
          </documentation>
        </annotation>
      </attribute>
      <attribute name="expression" type="string">
        <annotation>
          <documentation>
            Specifies an expression that maps the property, eg. ${street.address}
          </documentation>
        </annotation>
      </attribute>
    </complexType>
  </element>
  
  <attribute name="class">
    <annotation>
      <documentation>
        Service Task attribute for specifying a fully qualified Java class
        name. The Java class must implement either
        org.activiti.engine.delegate.JavaDelegate or
        org.activiti.engine.impl.pvm.delegate.ActivityBehavior
      </documentation>
    </annotation>
    <simpleType>
      <restriction base="string">
        <!-- regular expression taken from http://regexlib.com/REDetails.aspx?regexp_id=2821 -->
        <pattern value="([a-z]{2,3}(\.[a-zA-Z][a-zA-Z_$0-9]*)*)\.([A-Z][a-zA-Z_$0-9]*)"/>
      </restriction>
    </simpleType>
  </attribute>
  
  <attribute name="type">
    <annotation>
      <documentation>
        Service Task attribute specifying a built-in service task implementation.
      </documentation>
    </annotation>
    <simpleType>
      <restriction base="string">
        <enumeration value="mail"/>
      </restriction>
    </simpleType>
  </attribute>
  
  <attribute name="resultVariableName" type="string">
    <annotation>
      <documentation>
        Attribute on Service and Script Task corresponding with a process variable name.
        The result of executing the service task logic or the script will be stored 
        in this process variable.
      </documentation>
    </annotation>
  </attribute>
  
  <attribute name="expression" type="string">
    <annotation>
      <documentation>
        Allows to specify an expression that is evaluated at runtime.     
      </documentation>
    </annotation>
  </attribute>
  
  <attribute name="delegateExpression">
    <annotation>
      <documentation>
        Allows to specify an expression on a service task, taskListener or executionListener
        that at runtime must resolve to an object that implements the corresponsing
        interface (JavaDelegate, ActivityBehavior, TaskListener, ExecutionListener, etc.)
      </documentation>
    </annotation>
  </attribute>

  <element name="field">
    <annotation>
      <documentation>
        Extension Element for Service Tasks to inject values into the fields of
        delegate classes.
      </documentation>
    </annotation>
    <complexType>
      <choice minOccurs="0" maxOccurs="1">
      	<element name="string" type="string" />
      	<element name="expression" type="tns:tExpression" />
      </choice>
      <attribute name="name" type="string" use="required"/>
      <attribute name="stringValue" type="string" use="optional" />
      <attribute name="expression" type="tns:tExpression" use="optional" />
    </complexType>
  </element>
  
  <simpleType name="tExpression">
    <annotation>
      <documentation>
        Expression using the language declared in the expressionLanguage
        attribute of BPMN's definitions element.
      </documentation>
    </annotation>
    <restriction base="string">
    </restriction>
  </simpleType>
  
  <attribute name="assignee" type="string">
    <annotation>
      <documentation>
        User Task attribute to set the human performer of a user task.
        Also supports expressions that evaluate to a String.
      </documentation>
    </annotation>
  </attribute>
  
  <attribute name="candidateUsers">
    <annotation>
      <documentation>
        User Task attribute to set the potential owners of a user task.
        The provided user(s) will be candidate for performing the user task.
        In case of multiple user ids, a comma-separated list must be provided.
        Also supports expressions that evaluate to a String or Collection&lt;String&gt;.
      </documentation>
    </annotation>
  </attribute>
  
  <attribute name="candidateGroups">
    <annotation>
      <documentation>
        User Task attribute to set the potential owners of a user task.
        The provided group(s) will be candidate for performing the user task.
        In case of multiple group ids, a comma-separated list must be provided.
        Also supports expressions that evaluate to a String or Collection&lt;String&gt;.
      </documentation>
    </annotation>
  </attribute>

  <element name="taskListener">
    <annotation>
      <documentation>
        Extension element for User Tasks used to execute custom Java logic or an 
        expression upon the occurrence of a certain event. 
      </documentation>
    </annotation>
    <complexType>
      <sequence minOccurs="0" maxOccurs="unbounded">
        <element ref="tns:field" />
      </sequence>
      <attribute name="class" type="string">
        <annotation>
          <documentation>
            An implementation of the org.activiti.engine.impl.pvm.delegate.TaskListener interface
            that will be called when the task event occurs.
          </documentation>
        </annotation>
      </attribute>
      <attribute name="expression" type="tns:tExpression">
        <annotation>
          <documentation>
            Expression that will be evaluated when the task event occurs.
          </documentation>
        </annotation>
      </attribute>
      <attribute name="delegateExpression" type="tns:tExpression">
        <annotation>
          <documentation>
            Expression that must resolve to an object implementing a compatible interface
            for a taskListener. Evaluation and delegation to the resulting object is done
            when the task event occurs.
          </documentation>
        </annotation>
      </attribute>
      <attribute name="event" use="required">
        <annotation>
          <documentation>
            The event on which the delegation class or expression will be executed.
          </documentation>
        </annotation>
        <simpleType>
          <restriction base="string">
            <enumeration value="create" />
            <enumeration value="assignment" />
            <enumeration value="complete" />
          </restriction>
        </simpleType>
      </attribute>
    </complexType>
  </element>
  
  <element name="executionListener">
    <annotation>
      <documentation>
        Extension element for any activities and sequenceflow, used to execute custom Java logic or an 
        expression upon the occurrence of a certain event. 
      </documentation>
    </annotation>
    <complexType>
      <sequence minOccurs="0" maxOccurs="unbounded">
        <element ref="tns:field" />
      </sequence>
      <attribute name="class" type="string">
        <annotation>
          <documentation>
            An implementation of the org.activiti.engine.impl.pvm.delegate.ExecutionListener interface
            that will be called when the event occurs.
          </documentation>
        </annotation>
      </attribute>
      <attribute name="expression" type="tns:tExpression">
        <annotation>
          <documentation>
            Expression that will be evaluated when the event occurs.
          </documentation>
        </annotation>
      </attribute>
      <attribute name="delegateExpression" type="tns:tExpression">
        <annotation>
          <documentation>
            Expression that must resolve to an object implementing a compatible interface
            for an executionListener. Evaluation and delegation to the resulting object is done
            when the task event occurs.
          </documentation>
        </annotation>
      </attribute>
      <attribute name="event" use="optional">
        <annotation>
          <documentation>
            The event on which the delegation class or expression will be executed.
          </documentation>
        </annotation>
        <simpleType>
          <restriction base="string">
            <enumeration value="start" />
            <enumeration value="end" />
            <enumeration value="take" />
          </restriction>
        </simpleType>
      </attribute>
    </complexType>
  </element>
  
  <element name="in">
    <annotation>
      <documentation>
        Element to specify Data Input in Activiti Shortcuts
        (compare to DataInputAssociation in BPMN)
      </documentation>
    </annotation>
    <complexType>
      <attribute name="source" type="string" use="optional"/>
      <attribute name="sourceExpression" type="tns:tExpression" use="optional"/>
      <attribute name="target" type="string" use="required" />
    </complexType>
  </element>
  <element name="out">
    <annotation>
      <documentation>
        Element to specify Data Output in Activiti Shortcuts
        (compare to DataOutputAssociation in BPMN)
      </documentation>
    </annotation>
    <complexType>
      <attribute name="source" type="string" use="optional"/>
      <attribute name="sourceExpression" type="tns:tExpression" use="optional"/>
      <attribute name="target" type="string" use="required" />
    </complexType>
  </element>
  
  
</schema>
