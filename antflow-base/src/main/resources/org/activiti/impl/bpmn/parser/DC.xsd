<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" targetNamespace="http://www.omg.org/spec/DD/20100524/DC" elementFormDefault="qualified" attributeFormDefault="unqualified">

	<xsd:element name="Font" type="dc:Font" />
	<xsd:element name="Point" type="dc:Point" />
	<xsd:element name="Bounds" type="dc:Bounds" />
	
	<xsd:complexType name="Font">
		<xsd:attribute name="name" type="xsd:string" />
		<xsd:attribute name="size" type="xsd:double" />
		<xsd:attribute name="isBold" type="xsd:boolean" />
		<xsd:attribute name="isItalic" type="xsd:boolean" />
		<xsd:attribute name="isUnderline" type="xsd:boolean" />
		<xsd:attribute name="isStrikeThrough" type="xsd:boolean" />
	</xsd:complexType>
	
	<xsd:complexType name="Point">
		<xsd:attribute name="x" type="xsd:double" use="required" />
		<xsd:attribute name="y" type="xsd:double" use="required" />
	</xsd:complexType>
	
	<xsd:complexType name="Bounds">
		<xsd:attribute name="x" type="xsd:double" use="required" />
		<xsd:attribute name="y" type="xsd:double" use="required" />
		<xsd:attribute name="width" type="xsd:double" use="required" />
		<xsd:attribute name="height" type="xsd:double" use="required" />
	</xsd:complexType>

</xsd:schema>
