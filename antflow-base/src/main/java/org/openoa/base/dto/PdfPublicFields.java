package org.openoa.base.dto;

import com.itextpdf.text.Document;
import com.itextpdf.text.pdf.PdfWriter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 用户登录请求体
 *
 */
@Data
public class PdfPublicFields implements Serializable {

    private static final long serialVersionUID = 3191241716373120793L;

	/**
	 * 文档对象
	 */
	private Document document;

	/**
	 * PDF写入器
	 */
	private PdfWriter writer;

	/**
	 * PDF标题
	 */
	private String pdfTitle = "";

	/**
	 * 水印内容
	 */
	private String waterMark = "";

	/**
	 * 审批编号
	 */
    private String approvalNumber = "";

	/**
	 * 申请人对应的公司主体
	 */
	private String companyName = "";

	/**
	 * 申请人（员工id）
	 */
	private String empId;

	/**
	 * 申请人
	 */
    private String empName = "";

	/**
	 * 申请人部门
	 */
    private String deptName = "";
    private String deptId = "";

	/**
	 * 申请人提交时间
	 */
    private Date submitTime;

	/**
	 * 申请结束时间
	 */
    private Date endTime;

	/**
	 * 当前审批状态
	 */
    private String approvalStatusName = "";

	/**
	 * 模板id
	 */
	private Long bpmnConfId;


	/**
	 * 申请内容
	 */
	private List<ApprovalContent> approvalContents;

	/**
	 * 审批流程
	 */
	private List<ApprovalRecord> approvalRecords;


	/**
	 * 流程节点信息
	 */
	@Data
	@Builder
	public static class ApprovalRecord implements Serializable {
		private static final long serialVersionUID = 1L;
		/**
		 * 流程环节
		 */
		private String taskName;

		/**
		 * 审批人
		 */
		private String verifyUserName;

		/**
		 * 操作
		 */
		private String verifyStatusName;

		/**
		 * 审批意见
		 */
		private String verifyDesc;

		/**
		 * 审批时间
		 */
		private Date verifyDate;
	}

	/**
	 * 带顺序的字段名、字段值
	 */
	@Data
	@Builder
	@AllArgsConstructor
	@NoArgsConstructor
	public static class ApprovalContent implements Serializable {
		private static final long serialVersionUID = 1L;
		/**
		 * widget组件类型
		 */
		private String type = "";

		/**
		 * 字段名称
		 */
		private String fieldName;

		/**
		 * 字段Id
		 */
		private String fieldId;

		/**
		 * 字段值
		 */
		private String fieldValue;
	}
}
