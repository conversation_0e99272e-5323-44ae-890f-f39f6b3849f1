package org.openoa.base.service;

import org.openoa.base.entity.SyncEhrOrgInfo;
import org.openoa.base.vo.BaseIdTranStruVo;
import org.openoa.base.vo.OrgEmpTagInfoVo;
import org.openoa.base.vo.OrgEmpTreeVo;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface AfUserService {
    List<BaseIdTranStruVo> queryByNameFuzzy(String userName);

    List<BaseIdTranStruVo> queryCompanyByNameFuzzy(String companyName);

    List<BaseIdTranStruVo> queryUserByIds(Collection<String> userIds);

    BaseIdTranStruVo getById(String id);

    List<BaseIdTranStruVo> queryLeadersByEmployeeIdAndTier(String employeeId, Integer tier);

    List<BaseIdTranStruVo> queryLeadersByEmployeeIdAndGrade(String employeeId, Integer grade);

    BaseIdTranStruVo queryLeaderByEmployeeIdAndLevel(String employeeId, Integer level);

    BaseIdTranStruVo queryEmployeeHrpbByEmployeeId(String employeeId);

    BaseIdTranStruVo queryEmployeeDirectLeaderById(String employeeId);

    /**
     * 根据ehrSource获取组织架构树
     * @param ehrSource
     * @return
     */
    OrgEmpTreeVo getOrgEmpTreeBySource(String ehrSource, Integer type);

    /**
     * 根据名称模糊查询
     * @param name
     * @return
     */
    List<OrgEmpTagInfoVo> queryUserByNameFuzzy(String name);

    /**
     * 根据组织名称模糊查询
     * @param name
     * @return
     */
    List<OrgEmpTagInfoVo> queryTagByNameFuzzy(String name);

    /**
     * 根据组织名称模糊查询
     * @param name
     * @return
     */
    List<OrgEmpTagInfoVo> queryOrgByNameFuzzy(String name);

	/**
	 * 根据部门列表获取orgId与对应全链orgName的映射关系
	 * @param orgInfos List<SyncEhrOrgInfo>
	 * @return Map
	 */
	Map<String, String> getNamePath(List<SyncEhrOrgInfo> orgInfos, Boolean isOrg);

    /**
     * 获取执行组织下的所有员工
     * @param orgId
     * @param ehrSource
     * @return
     */
    Set<String> getUserIdsByOrgId(String orgId, String ehrSource);
}
