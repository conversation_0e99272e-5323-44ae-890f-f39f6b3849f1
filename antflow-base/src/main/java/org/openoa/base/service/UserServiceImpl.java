package org.openoa.base.service;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.openoa.base.constant.enums.CustomFieldEnum;
import org.openoa.base.constant.enums.EmpOrOrgTreeTypeEnum;
import org.openoa.base.constant.enums.EmpOrgTypeEnum;
import org.openoa.base.dto.wecom.EhrSourceParent;
import org.openoa.base.entity.Role;
import org.openoa.base.entity.SyncEhrOrgInfo;
import org.openoa.base.entity.User;
import org.openoa.base.mapper.SyncEhrOrgInfoMapper;
import org.openoa.base.mapper.UserMapper;
import org.openoa.base.vo.BaseIdTranStruVo;
import org.openoa.base.vo.OrgEmpTagInfoVo;
import org.openoa.base.vo.OrgEmpTreeVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 此类为流程引擎中用户服务,AntFlow完全接管了activiti中的用户系统
 * 此类中绝大部分依赖t_user demo表,用户早期为快速测试可以使用这个表,但是和生产上强烈建议替换为自己的用户表
 * 替换很简单,每个系统都有自己的用户表,绝大部分场景只需要查出用户的id和name即可(如果您的系统不是叫id和name,sql中需要使用as转成id和name)
 * 此类为默认实现,用户可以将sql替换为自己系统的sql,也可以自己实现AfUserService(方法较多,但是非必须都实现,前四个必须要实现的,后面的等运行时报错了,再实现也不迟到,不然一下子会很懵逼,不知道要干什么)
 * 前四个实现以后,基本的流程流转就ok了,后面的可以用到指定的demo时报错了再看修改,这些都是相对高级一些的功能,等你用到这部分的,也大概有一些基本的概念了,这时候再改些也相对较快了
 * 用户也可以不重写sql,而是自己实现AfUserService,然后将自己的实现标为@Primary即可
 * 这里可能有一个误区,用户信息不是必须查sql(但是一般情况下是),只需要实现指定方法将用户的id和名称拿到即可,至于你是怎么拿的antflow不关心.比如你的用户信息是调用三方Restful接口返回的,返回结果只要封装成BaseIdTranStruVo即可
 */
@Service("afUserService")
@Slf4j
public class UserServiceImpl implements AfUserService{
    @Autowired
    UserMapper userMapper;

    @Resource
    private SyncEhrOrgInfoService syncEhrOrgInfoService;

    @Resource
    private TRoleService tRoleService;
	@Autowired
	private SyncEhrOrgInfoMapper syncEhrOrgInfoMapper;


	@Override
    public List<OrgEmpTagInfoVo> queryUserByNameFuzzy(String name) {
        List<OrgEmpTagInfoVo> users = userMapper.queryUserByNameFuzzy(name);
        if (CollectionUtils.isEmpty(users)){
            return Collections.emptyList();
        }
        users.forEach(user -> user.setType(CustomFieldEnum.MEMBER.getNo()));
		if (!CollectionUtils.isEmpty(users)) {
			List<SyncEhrOrgInfo> orgInfos = syncEhrOrgInfoService.list(new LambdaQueryWrapper<SyncEhrOrgInfo>().in(SyncEhrOrgInfo::getOrgId, Lists.transform(users, OrgEmpTagInfoVo::getOrgIdFromUser)));
			if (!CollectionUtils.isEmpty(orgInfos)) {
				Map<String, String> namePathMap = getNamePath(orgInfos, false);
				users.forEach(user -> {
					String orgId = user.getOrgIdFromUser();
					String ehrSource = user.getEhrSource();
					if (StringUtils.isNotBlank(orgId) && StringUtils.isNotBlank(ehrSource)) {
						String key = ehrSource + ":" + orgId;
						String namePath = namePathMap.getOrDefault(key, "");
						user.setNamePath(namePath);
					}
				});
			}
		}
        return users;
    }

    @Override
    public List<OrgEmpTagInfoVo> queryTagByNameFuzzy(String name) {
        List<Role> tags = tRoleService.list(new LambdaQueryWrapper<Role>().like(Role::getRoleName, name).eq(Role::getIsDel, 0));
        if (CollectionUtils.isEmpty(tags)){
            return Collections.emptyList();
        }
        return tags.stream()
                .map(t -> new OrgEmpTagInfoVo(CustomFieldEnum.TAG.getNo(),
                        null,
                        t.getId().toString(), t.getRoleName())).collect(Collectors.toList());
    }

    @Override
    public List<OrgEmpTagInfoVo> queryOrgByNameFuzzy(String name) {
        List<SyncEhrOrgInfo> orgInfos = syncEhrOrgInfoService.list(new LambdaQueryWrapper<SyncEhrOrgInfo>().like(SyncEhrOrgInfo::getOrgName, name));
        if (CollectionUtils.isEmpty(orgInfos)){
            return Collections.emptyList();
        }
	    Map<String, String> orgIdNamesMap = getNamePath(orgInfos, true);
	    return orgInfos.stream()
                .map(org -> new OrgEmpTagInfoVo(CustomFieldEnum.ORG.getNo(), org.getEhrSource().toString(), org.getEhrSource()+"_"+org.getOrgId(), org.getOrgName(), orgIdNamesMap.getOrDefault(org.getOrgId(), "") + ":" + org.getEhrSource()))
                .collect(Collectors.toList());
    }


	/**
	 * 根据org列表获取全链路的orgName
	 * @param orgInfos List<SyncEhrOrgInfo>
	 * @return map
	 */
	public Map<String, String> getNamePath(List<SyncEhrOrgInfo> orgInfos, Boolean isOrg) {
		Map<Integer, Set<String>> ehrSourceToOrgIds = new HashMap<>();
		for (SyncEhrOrgInfo org : orgInfos) {
			if (StringUtils.isNotBlank(org.getOrgPath())) {
				String[] ids = org.getOrgPath().split("/");
				for (String id : ids) {
					ehrSourceToOrgIds.computeIfAbsent(org.getEhrSource(), k -> new HashSet<>()).add(id);
				}
			}
		}
		List<SyncEhrOrgInfo> allRelatedOrgs = new ArrayList<>();
		for (Map.Entry<Integer, Set<String>> entry : ehrSourceToOrgIds.entrySet()) {
			Integer ehrSource = entry.getKey();
			Set<String> orgIds = entry.getValue();
			List<SyncEhrOrgInfo> relatedOrgs = syncEhrOrgInfoService.list(new LambdaQueryWrapper<SyncEhrOrgInfo>().eq(SyncEhrOrgInfo::getEhrSource, ehrSource).in(SyncEhrOrgInfo::getOrgId, orgIds));
			allRelatedOrgs.addAll(relatedOrgs);
		}

		Map<String, String> idNameMap = allRelatedOrgs.stream()
			.filter(info -> StringUtils.isNotBlank(info.getOrgId()) && StringUtils.isNotBlank(info.getOrgName()))
			.collect(Collectors.toMap(info -> info.getEhrSource() + ":" + info.getOrgId(), SyncEhrOrgInfo::getOrgName, (o1, o2) -> o1));
		// 拼接完整路径的namePath路径
		Map<String, String> orgIdNamesMap = new HashMap<>();
		for (SyncEhrOrgInfo org : orgInfos) {
			String orgId = org.getOrgId();
			String orgPath = org.getOrgPath();
			Integer ehrSource = org.getEhrSource();

			if (StringUtils.isBlank(orgId) || StringUtils.isBlank(orgPath)) continue;

			String namePath = Arrays.stream(orgPath.split("/"))
				.map(id -> idNameMap.get(ehrSource + ":" + id))
				.filter(StringUtils::isNotBlank)
				.filter(x -> {
					if (isOrg) { // 如果是针对部门的查询，就不拼接当前的部门，如：当前部门是【AI产品组】，则namePath=【中企美业/产品部】
						return !Objects.equals(x, org.getOrgName());
					}
					return true; // 否则就就是用户的查询，查询用户时显示完整链路的部门名称 如：当前用户是【张文康】，则namePath=【中企/苏皖区/合肥分公司/商务3部】
				})
				.collect(Collectors.joining("/"));
			log.info("*********>>> orgPath={}, namePath= {}", orgPath, namePath);
			orgIdNamesMap.put(ehrSource + ":" + orgId, namePath);
		}
		return orgIdNamesMap;
	}

	@Override
    public List<BaseIdTranStruVo> queryByNameFuzzy(String userName) {
        List<BaseIdTranStruVo> users = userMapper.queryByNameFuzzy(userName);
        return users;
    }

    @Override
    public List<BaseIdTranStruVo> queryCompanyByNameFuzzy(String companyName) {
        List<BaseIdTranStruVo> baseIdTranStruVos = userMapper.queryCompanyByNameFuzzy(companyName);
        return baseIdTranStruVos;
    }

    @Override
    public List<BaseIdTranStruVo> queryUserByIds(Collection<String> userIds){

        List<BaseIdTranStruVo> users = userMapper.queryByIds(userIds);
        return users;
    }
    @Override
    public BaseIdTranStruVo getById(String id){
        List<BaseIdTranStruVo> users = userMapper.queryByIds(Lists.newArrayList(id));
        if(CollectionUtils.isEmpty(users)){
            return new BaseIdTranStruVo();
        }
        return users.get(0);
    }
    @Override
    public  List<BaseIdTranStruVo> queryLeadersByEmployeeIdAndTier(String employeeId, Integer tier){
        List<BaseIdTranStruVo> users = userMapper.getLevelLeadersByEmployeeIdAndTier(employeeId,tier);
        return users;
    }

    /**
     * dummy sql to be implement
     * @param employeeId
     * @param grade
     * @return
     */
    @Override
    public  List<BaseIdTranStruVo> queryLeadersByEmployeeIdAndGrade(String employeeId, Integer grade){
        List<BaseIdTranStruVo> users = userMapper.getLevelLeadersByEmployeeIdAndEndGrade(employeeId,grade);
        return users;
    }
    /**
     * dummy sql to be implement
     * @param employeeId
     * @param level setting
     * @return
     */
    @Override
    public BaseIdTranStruVo queryLeaderByEmployeeIdAndLevel(String employeeId, Integer level){

        return userMapper.getLeaderByLeventDepartment(employeeId,level);
    }
    @Override
    public BaseIdTranStruVo queryEmployeeHrpbByEmployeeId(String employeeId){
        BaseIdTranStruVo baseIdTranStruVo = userMapper.getHrpbByEmployeeId(employeeId);
        return baseIdTranStruVo;
    }
    @Override
    public BaseIdTranStruVo queryEmployeeDirectLeaderById(String employeeId){
        BaseIdTranStruVo baseIdTranStruVo = userMapper.getDirectLeaderByEmployeeId(employeeId);
        return baseIdTranStruVo;
    }

    @Override
    public Set<String> getUserIdsByOrgId(String orgId, String ehrSource) {
        OrgEmpTreeVo empTreeVo = getOrgEmpTreeBySource(ehrSource, EmpOrOrgTreeTypeEnum.EMP_TYPE.getCode());

        Set<String> userIds = new HashSet<>();
        // 使用队列进行辅助，广度优先遍历该树结构
        Queue<OrgEmpTreeVo> queue = new LinkedList<>();

        boolean isFindRootOrgId = false;
        queue.offer(empTreeVo);
        while (!queue.isEmpty()) {
            OrgEmpTreeVo node = queue.poll();

            if (isFindRootOrgId) {
                if (node.getType().equals(EmpOrOrgTreeTypeEnum.EMP_TYPE.getCode())) {
                    userIds.add(node.getValue());
                } else {
                    queue.addAll(node.getChildren());
                }
            } else {
                if (node.getType().equals(EmpOrOrgTreeTypeEnum.ORG_TYPE.getCode())) {

                    if (node.getValue().equals(ehrSource + "_" + orgId)) {
                        queue.clear();
                        isFindRootOrgId = true;
                    }

                    queue.addAll(node.getChildren());
                }
            }
        }
        return userIds;
    }

    @Override
    public OrgEmpTreeVo getOrgEmpTreeBySource(String ehrSource, Integer type) {

        OrgEmpTreeVo head = null;
	    List<EhrSourceParent> ehrSourceParents = syncEhrOrgInfoMapper.selectRootParentList();
		log.info("getOrgTree selectRootParentList result={}", JSON.toJSONString(ehrSourceParents));
	    Map<Integer, String> ehrSourceRootParentIdMap = Optional.ofNullable(ehrSourceParents).orElse(new ArrayList<>()).stream().collect(Collectors.toMap(EhrSourceParent::getEhrSource, EhrSourceParent::getTopParentId, (o,n) -> n));
	    String rootParentId = ehrSourceRootParentIdMap.getOrDefault(Integer.valueOf(ehrSource), "0");
	    // 获取组织列表
        List<SyncEhrOrgInfo> ehrOrgInfoList = syncEhrOrgInfoService.getOrgEmpTreeBySource(ehrSource);
        Map<String, OrgEmpTreeVo> orgs = ehrOrgInfoList.stream()
                .map(i -> new OrgEmpTreeVo(i.getEhrSource() + "_" + i.getOrgId(), i.getOrgName(), i.getEhrSource() + "_" + i.getParentId(), EmpOrgTypeEnum.ORG.getCode()))
                .collect(Collectors.toMap(OrgEmpTreeVo::getValue, org -> org, (o,n) -> n));
	    Map<String, List<OrgEmpTreeVo>> userGroup = new HashMap<>();
		if (EmpOrOrgTreeTypeEnum.EMP_TYPE.getCode().equals(type)) {
			List<User> users = userMapper.getUserBySource(ehrSource);

			// 将员工按orgId分组，并将分组后的员工转为EmpInfo
			userGroup = users.stream()
				.collect(
					Collectors.groupingBy(
						u -> u.getEhrSource() + "_" + u.getOrgId(),
						Collectors.mapping(user -> new OrgEmpTreeVo(user.getId(), user.getUserName(), EmpOrgTypeEnum.EMP.getCode()), Collectors.toList())
					)
				);
		}
		// 一个主体的根组织下面是否有多个org组织
	    boolean rootSubOrgCount = syncEhrOrgInfoService.lambdaQuery().eq(SyncEhrOrgInfo::getEhrSource, Integer.valueOf(ehrSource)).eq(SyncEhrOrgInfo::getParentId, rootParentId).count() > 1;
	    // 用于标记是否已经设置过 head
	    boolean hasSetHead = false;
        for (Map.Entry<String, OrgEmpTreeVo> entry : orgs.entrySet()) {
            OrgEmpTreeVo org = entry.getValue();
            String orgId = org.getValue();

			if (EmpOrOrgTreeTypeEnum.EMP_TYPE.getCode().equals(type)) {
				if (userGroup.containsKey(orgId)) {
					org.getChildren().addAll(userGroup.get(orgId));
				}
			}

            if (Objects.equals(org.getParentId().split("_")[1], rootParentId)) {
	            if (!hasSetHead) {
		            hasSetHead = true;
		            if (rootSubOrgCount) { // 多个根组织，构造虚拟 root，并挂上当前 org
			            head = new OrgEmpTreeVo("virtual_root", "ROOT", rootParentId, type);
			            head.getChildren().add(org);
		            } else {
			            head = org;
		            }
	            } else {
		            head.getChildren().add(org);
	            }
            } else {
                String parentId = org.getParentId();
                if (orgs.containsKey(parentId)) {
                    orgs.get(parentId).getChildren().add(org);
                }
            }
        }

        return head;
    }
}
