package org.openoa.base.service.wecom;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.openoa.base.constant.enums.EhrSourceEnum;
import org.openoa.base.dto.wecom.EmployeeInfo;
import org.openoa.base.dto.wecom.PrimaryEmpInfo;
import org.openoa.base.dto.wecom.PrimaryOrgInfo;
import org.openoa.base.dto.wecom.RespCodeEnum;
import org.openoa.base.entity.SyncEhrOrgInfo;
import org.openoa.base.entity.User;
import org.openoa.base.exception.JiMuBizException;
import org.openoa.base.mapper.UserMapper;
import org.openoa.base.service.SyncEhrOrgInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class EhrSyncService {

    @Value("${master-data.domain}")
    private String masterDataDomain;

    @Resource
    private GatewayApi gatewayApi;
	@Resource
	private SyncEhrOrgInfoService syncEhrOrgInfoService;
	private static final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

	/*
	 * 在 ehr_db，新网和辰星的ehr_source中，下面这些人其实是属于集团的（下面这些人是通过企微组织架构筛选出来的）
	 * select * from sync_ehr_employee_info where ehr_source = 3 AND name IN ('郭俊辉','刘瑞敏','吕占伟','王梦琦','袁金凤','贲博','沈刚','孙靖','王昕','吴敏','解佳兴','姚晨曦','叶常青','张琳','朱红娟','倪瑕','任旭杰','王皓','王明非','郑文婷','庞阳','胡明','王辰慧','陈诗琦','韩梦','马晓航','冯宇歆','王莹','侯晓瑶','王守房','杜鑫琪','潘佳新','田蕊','朱倩倩');
	 * select * from sync_ehr_employee_info where ehr_source = 4 AND name IN ('侯晓瑶','胡广斌','胡明','杨亭','李月','田蕊','王皓','王诗缘','夏春明','周璐','朱倩倩','孙靖','吕占伟','袁金凤','沈刚','叶常青','贲博','陈诗琦','冯宇歆','马晓航','倪瑕','王昕','王莹','魏玉娜','解佳兴','张琳','朱红娟','韩梦','王辰慧','吴敏');
	 */
	private static final List<String> MANUAL_FILTRATION_EMP_EMAILS = Lists.newArrayList(
		// 新网需要手动过滤非300.cn结尾的员工邮箱
		"<EMAIL>",
		"<EMAIL>",
		"<EMAIL>",
		"<EMAIL>",

		// 辰星需要手动过滤非300.cn结尾的员工邮箱
		"<EMAIL>",
		"<EMAIL>",
		"<EMAIL>"
	);
	@Autowired
	private UserMapper userMapper;

	/**
     * 获取所有的机构信息
     * TODO
     * @return
     */
    public List<PrimaryOrgInfo> getAllOrganizationData() {
        String url = masterDataDomain + "/ehrData/sync/getEhrOrgList";
        Map<String, Object> param = new HashMap<>();
        log.info("调用主数据系统，获取所有机构下员工信息");
        String result = gatewayApi.request(url, param);
        // log.info("调用主数据系统，获取所有机构下员工信息，结果为:{}", result);
        JSONObject jsonObject;
        List<JSONObject> dataArray;
        if (StringUtils.isEmpty(result) || (jsonObject = JSON.parseObject(result)) == null
                || !"200".equals(jsonObject.getString("code")) || CollectionUtils.isEmpty(dataArray = JSON.parseArray(jsonObject.getString("data"), JSONObject.class))) {
            log.warn("调用主数据系统，获取所有机构下员工信息，返回结果为空");
            throw new JiMuBizException(RespCodeEnum.Z0203.getRespCode(), "获取所有机构下员工信息失败");
        }
        return dataArray.stream().map(PrimaryOrgInfo::parse).collect(Collectors.toList());
    }

    /***
     * 根据员工id获取所有记录
     * @param businessIds
     * <AUTHOR>
     * @date 2025/2/24 11:18
     * @version 1.0.0
     * @return java.util.List<com.ce.contract.contractInst.service.third.entity.EmployeeInfo>
     **/
    public List<EmployeeInfo> getEmployeeInfoByBusinessIds(String businessIds) {
        String url = masterDataDomain + "/ehrData/sync/getEmployeesByEmpId";
        Map<String, Object> param = new HashMap<>();
        param.put("empIds", businessIds);
        log.info("调用主数据系统，根据员工id获取所有记录，参数为:{}", JSON.toJSONString(param));
        String result = gatewayApi.request(url, param);
        log.info("调用主数据系统，根据员工id获取所有记录，结果为:{}", result);
        JSONObject jsonObject;
        if (StringUtils.isEmpty(result) || (jsonObject = JSON.parseObject(result)) == null || !"200".equals(jsonObject.getString("code"))) {
            log.error("调用主数据系统，根据员工id获取所有记录，返回结果为空，参数:{}", JSON.toJSONString(param));
            throw new JiMuBizException(RespCodeEnum.Z0203.getRespCode(), "获取员工数据失败");
        }
        JSONObject data = JSONObject.parseObject(result);
        JSONArray array = data.getJSONArray("data");
        if (null == array || array.isEmpty()) {
            log.warn("当前员工不存在，需要注意手动操作，参数:{}", JSON.toJSONString(param));
            return null;
        }
        return array.stream().map(x -> JSONObject.parseObject(x.toString())).map(EmployeeInfo::parse).collect(Collectors.toList());
    }

	/**
	 * 根据企微的userId获取对应的ehr信息
	 * @param source
	 * @param userId
	 * <AUTHOR>
	 * @date 2025/4/18 17:45
	 * @version 1.0.0
	 * @return com.ce.contract.contractInst.service.third.entity.EmployeeInfo
	 **/
	public EmployeeInfo getByWechatUserId(Integer source,String userId) {
		String url = masterDataDomain + "/ehrData/sync/getByWechatUserId";
		Map<String, Object> param = new HashMap<>();
		param.put("source", source);
		param.put("ehrEmpId", userId);
		log.info("调用主数据系统，获取员工信息，参数为:{}", JSON.toJSONString(param));
		String result = gatewayApi.request(url, param);
		log.info("调用主数据系统，获取员工信息，结果为:{}", result);
		JSONObject jsonObject;
		if (StringUtils.isEmpty(result) || (jsonObject = JSON.parseObject(result)) == null || !"200".equals(jsonObject.getString("code"))) {
			log.warn("调用主数据系统，获取员工信息，返回结果为空，ehrEmpId:{} source:{}", userId,source);
			throw new JiMuBizException(RespCodeEnum.Z0203.getRespCode(), "获取机构下员工数据失败");
		}
		jsonObject = JSON.parseObject(result);
		JSONObject data = jsonObject.getJSONObject("data");
		EmployeeInfo parse = EmployeeInfo.parse(data);
		log.info("获取员工信息 userId={} source={}", userId,source);
		return parse;
	}

	/**
	 * 校验登录用户
	 * @param userName
	 * @param auth
	 * <AUTHOR>
	 * @date 2025/2/25 10:54
	 * @version 1.0.0
	 * @return java.util.Optional<java.lang.String>
	 **/
	public EmployeeInfo checkLoginUserPwd(String userName, String auth) {
		Map<String, Object> param = new HashMap<>();
		param.put("userName", userName);
		param.put("auth", auth);
		String url = masterDataDomain + "/ehrData/sync/checkLoginUserPwd";
		log.info("param={}", JSON.toJSONString(param));
		String result = gatewayApi.request(url, param);
		JSONObject jsonObject;
		if (StringUtils.isEmpty(result) || (jsonObject = JSON.parseObject(result)) == null || !"200".equals(jsonObject.getString("code"))) {
			log.error("校验登录用户，返回结果为空，参数:{} result={}", JSON.toJSONString(param),result);
			throw new JiMuBizException(RespCodeEnum.Z0203.getRespCode(), "校验登录用户数据失败");
		}
		JSONObject data = jsonObject.getJSONObject("data");
		return EmployeeInfo.parse(data);
	}

	/**
	 * 根据ehrSource获取员工信息列表
	 * @param ehrSource ehrSource
	 * @param updateTime 上次更新时间，传null则忽略此条件
	 * @return List<PrimaryEmpInfo>
	 */
	public List<PrimaryEmpInfo> getEmployeeInfoBySource(Integer ehrSource, Date updateTime) {
		Map<String, Object> params = new HashMap<>();
		params.put("source", ehrSource);
		params.put("updateTime", updateTime);
		String url = masterDataDomain + "/ehrData/sync/getEmployeeInfoBySource";
		String result = gatewayApi.request(url, params);
		JSONObject jsonObject;
		if (StringUtils.isEmpty(result) || (jsonObject = JSON.parseObject(result)) == null || !"200".equals(jsonObject.getString("code"))) {
			log.error("查询EHR员工信息结果未空，入参:{}", JSON.toJSONString(params));
			throw new JiMuBizException(RespCodeEnum.Z0201.getRespCode(), "查询EHR员工信息结果未空");
		}

		List<JSONObject> data = JSON.parseArray(jsonObject.getString("data"), JSONObject.class);
		List<PrimaryEmpInfo> pulledPrimaryEmps = data.stream().map(PrimaryEmpInfo::parse).collect(Collectors.toList());

		// 新网和辰星的员工信息，需要排除掉集团的人员。由于新网和辰星跟其他组织是相互独立的ehr,所以不能根据orgPath来排除掉集团的人员，可以考虑根据邮箱排除一部分，剩余的根据手动写死的邮箱列表来排除，但是其中属于集团权限组的人需要保留在原位置不需要过滤
		if (ehrSource.equals(EhrSourceEnum.CX.getCode()) || ehrSource.equals(EhrSourceEnum.XINNET.getCode())) {
			return pulledPrimaryEmps.stream().filter(x -> StringUtils.isNotBlank(x.getEmail()))
				.filter(x -> {
					if (Objects.equals(x.getEmail(), "<EMAIL>")){
						return true;
					} else {
						return !x.getEmail().endsWith("@300.cn");
					}
				})
				.filter(x -> !MANUAL_FILTRATION_EMP_EMAILS.contains(x.getEmail()))
				.collect(Collectors.toList());
		}
		Set<String> orgIds = pulledPrimaryEmps.stream().map(PrimaryEmpInfo::getOrgId).collect(Collectors.toSet());
		if (CollectionUtils.isEmpty(orgIds)) {
			return pulledPrimaryEmps;
		}
		Map<String, String> orgIdOrgPathMap = Optional.ofNullable(
				syncEhrOrgInfoService.lambdaQuery().select(SyncEhrOrgInfo::getOrgId, SyncEhrOrgInfo::getOrgPath).in(SyncEhrOrgInfo::getOrgId, orgIds).list())
			.orElse(new ArrayList<>()).stream()
			.collect(Collectors.toMap(SyncEhrOrgInfo::getOrgId, SyncEhrOrgInfo::getOrgPath, (k1, k2) -> k2));
		for (PrimaryEmpInfo pulledPrimaryEmp : pulledPrimaryEmps) {
			String orgPath = orgIdOrgPathMap.get(pulledPrimaryEmp.getOrgId());
			if (StringUtils.isNotBlank(orgPath)) {
				String topOrgId = "";
				if (orgPath.startsWith("0/9/") || orgPath.startsWith("0/1/")) {
					String pathWithoutPrefix = orgPath.substring(4);
					int slashIndex = pathWithoutPrefix.indexOf('/');
					if (slashIndex > 0) {
						topOrgId = pathWithoutPrefix.substring(0, slashIndex);
					} else if (StringUtils.isNotBlank(pathWithoutPrefix)) {
						topOrgId = pathWithoutPrefix;
					}
				} if (orgPath.startsWith("9/") || orgPath.startsWith("1/")) {
					String pathWithoutPrefix = orgPath.substring(2);
					int slashIndex = pathWithoutPrefix.indexOf('/');
					if (slashIndex > 0) {
						topOrgId = pathWithoutPrefix.substring(0, slashIndex);
					} else if (StringUtils.isNotBlank(pathWithoutPrefix)) {
						topOrgId = pathWithoutPrefix;
					}
				}
				pulledPrimaryEmp.setTopOrgId(topOrgId);
			}
		}
		return pulledPrimaryEmps;
	}

	public String getOrgNameByUserId(String userId) {
		User userById = userMapper.getUserById(userId);
		if (Objects.nonNull(userById)) {
			return Optional.ofNullable(syncEhrOrgInfoService.lambdaQuery().select(SyncEhrOrgInfo::getOrgName)
				.eq(SyncEhrOrgInfo::getOrgId, userById.getOrgId())
				.eq(SyncEhrOrgInfo::getEhrSource, userById.getEhrSource()).last("LIMIT 1").one()).map(SyncEhrOrgInfo::getOrgName).orElse(null);
		}
		return null;
	}

}