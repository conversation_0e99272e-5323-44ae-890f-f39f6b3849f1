package org.openoa.base.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.openoa.base.dto.wecom.OrgEhrSourceDto;
import org.openoa.base.dto.wecom.OrgNode;
import org.openoa.base.entity.Employee;
import org.openoa.base.entity.User;
import org.openoa.base.vo.BaseIdTranStruVo;
import org.openoa.base.vo.OrgEmpTagInfoVo;
import org.openoa.base.vo.TaskMgmtVO;

import java.util.Collection;
import java.util.LinkedList;
import java.util.List;
import java.util.Set;


@Mapper
public interface UserMapper {
    // a nonce method
    List<BaseIdTranStruVo> queryCompanyByNameFuzzy(@Param("companyName") String companyName);
    //must be implemented
    List<BaseIdTranStruVo> queryByNameFuzzy(@Param("userName") String userName);

    //must be implemented
    List<BaseIdTranStruVo> queryByIds(@Param("userIds") Collection<String> userIds);
    //must be implemented
    Employee getEmployeeDetailById(@Param("employeeId") String id);
    //must be implemented
    List<Employee> getEmployeeDetailByIds(@Param("employeeIds")Collection<String> ids);
    long checkEmployeeEffective(@Param("employeeId") String id);

    //if you want to use level leader sign functions,you must implement it
    List<BaseIdTranStruVo> getLevelLeadersByEmployeeIdAndTier(@Param("employeeId") String employeeId,@Param("tier") Integer tier);
    List<BaseIdTranStruVo> getLevelLeadersByEmployeeIdAndEndGrade(@Param("employeeId") String employeeId,@Param("endGrade") Integer tier);
    BaseIdTranStruVo getHrpbByEmployeeId(@Param("employeeId") String employeeId);
    BaseIdTranStruVo getDirectLeaderByEmployeeId(@Param("employeeId") String employeeId);

    LinkedList<BaseIdTranStruVo> selectAll(@Param("roleId") Integer roleId);

    List<BaseIdTranStruVo> selectUserPageList(Page page, @Param("vo") TaskMgmtVO taskMgmtVO);

    BaseIdTranStruVo getLeaderByLeventDepartment(@Param("startUserId") String startUserId,@Param("assignLevelGrade")Integer departmentLevel);


    List<User> queryByUserIds(@Param("userIds") Collection<String> userIds);

    /**
     * 根据ehrSource获取用户信息
     * @param ehrSource
     * @return
     */
    List<User> getUserBySource(String ehrSource);

    /**
     * 根据ehrSource和ehr的员工id获取用户信息
     *
     * @param id
     * @param ehrSource
     * @return
     */
    User getUserByEmpId(@Param("id") String id,@Param("ehrSource") String ehrSource);

    /**
     * 根据id获取用户信息
     * @param id
     * @return
     */
    User getUserById(@Param("id") String id);

    /**
     * 获取用户的组织id
     * @param id 用户id
     * @return List<String>
     */

    OrgEhrSourceDto getOrgIdByUserId(String id);

    /**
     * 获取当前组织id和对应的父级id
     * @param orgId 组织id
     * @return OrgNode
     */
    OrgNode getOrgNodeByOrgIdAndEhrSource(@Param("orgId") String orgId, @Param("ehrSource") Integer ehrSource);

	/**
	 * 判断用户是否存在（在职 & 未删除）
	 * @param id t_user.id
	 * @return 用户数量
	 */
	Integer userExist(@Param("id") String id);

    /**
     * 根据组织名称模糊查询组织信息
     * @param name
     * @return
     */
    List<OrgEmpTagInfoVo> queryUserByNameFuzzy(@Param("name") String name);

	/**
	 * 转交给审核管理员的默认人员
	 */
	User getDefaultNodeHeaderActionUser(@Param("ehrSource") Integer ehrSource);

	/**
	 * 检查用户是否为管理员
	 * @param id
	 * @return
	 */
	Boolean checkUserAdmin(@Param("id") String id);

	/**
	 * 逻辑删除用户
	 * @param ids 用户ids
	 */
	void logicDeleteUserByIds(@Param("ids") List<String> ids);

	void truncateUserTable();

    /**
     * 获取组织下所有员工
     * @param orgId
     * @return
     */
    Set<String> listByOrgId(@Param("orgId") String orgId);
}
