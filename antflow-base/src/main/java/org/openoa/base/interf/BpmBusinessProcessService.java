package org.openoa.base.interf;

import org.openoa.base.entity.BpmBusinessProcess;
import org.openoa.base.vo.BpmBusinessProcessExportVo;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface BpmBusinessProcessService {
    BpmBusinessProcess getBpmBusinessProcess(String processCode);

	/**
	 * 合并人员信息后，修改历史的业务数据，包括[我的发起、我的待办、我的已办、抄送到我]
	 * @param historyUserIds 修改前的用户id列表
	 * @param targetUserId 修改后的用户id
	 */
	void updateMergedUserInfoHistory(List<String> historyUserIds, String targetUserId);

	/**
	 * 导出审批数据
	 * @param response
	 * @param bpmBusinessProcessExportVo
	 */
	void exportToExcel(HttpServletResponse response, BpmBusinessProcessExportVo bpmBusinessProcessExportVo) throws Exception;
}
